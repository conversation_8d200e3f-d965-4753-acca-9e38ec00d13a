import requests
import json
import base64
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

# 获取当天和明天的日期格式化字符串
today = datetime.now()
tomorrow = today + timedelta(days=1)
formatted_date = today.strftime("%Y-%m-%d")
formatted_tomorrow_date = tomorrow.strftime("%Y-%m-%d")

class APIClient:
    def __init__(self):
        self.session = requests.session()
        self.is_logged_in = False
        self.booking_select_time_data = None
        self.booking_date_ = None
        self.booking_seat = None
        self.booking_space = None
        self.booking_time = None
        self.booking_user_authorization = None
        self.booking_type = '1'  # 默认为座位预约类型

    def login(self, username, password):
        try:
            if True:
                user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                zjuam_login_url = "http://zjuam.zju.edu.cn/cas/login?service=https%3A%2F%2Fbooking.lib.zju.edu.cn%2Fapi%2Fcas%2Fcas"

                zjuam_login_resp = self.session.get(zjuam_login_url)
                zjuam_pubkey_url = 'https://zjuam.zju.edu.cn/cas/v2/getPubKey'
                zjuam_pubkey_resp = self.session.get(zjuam_pubkey_url)

                # RSA加密密码
                password_bytes = bytes(password, 'ascii')
                password_int = int.from_bytes(password_bytes, 'big')
                e_int = int(zjuam_pubkey_resp.json()["exponent"], 16)
                M_int = int(zjuam_pubkey_resp.json()["modulus"], 16)
                result_int = pow(password_int, e_int, M_int)
                encrypt_password = hex(result_int)[2:].rjust(128, '0')

                # 登录请求
                zjuam_login_data = {
                    'username': username,
                    'password': encrypt_password,
                    '_eventId': 'submit',
                    'execution': BeautifulSoup(zjuam_login_resp.text, "html.parser").find("input", attrs={'name':'execution'})['value'],
                    'authcode': '',
                }
                
                zjuam_login_resp = self.session.post(zjuam_login_url, headers={'User-Agent': user_agent}, data=zjuam_login_data, verify=False)

                if "用户名或密码错误" in zjuam_login_resp.text:
                    self.is_logged_in = False
                    return False

                # 获取用户信息
                booking_user_url = 'http://booking.lib.zju.edu.cn/api/cas/user'
                booking_user_data = {
                    'cas': zjuam_login_resp.url[zjuam_login_resp.url.find('cas=') + 4:]
                }

                booking_user_resp = self.session.post(booking_user_url, headers={'User-Agent': user_agent}, data=booking_user_data)
                self.booking_user_data = json.loads(booking_user_resp.text)
                self.booking_user_authorization = 'bearer' + self.booking_user_data['member']['token']
            else:
                self.booking_user_authorization = 'debug'
            print(f"登录成功: {self.booking_user_authorization}")
            
            self.is_logged_in = True
            return True
        except Exception as e:
            print(f"登录失败: {str(e)}")
            self.is_logged_in = False
            return False
    
    def set_booking_type(self, booking_type):
        """设置预约类型: '1'为座位预约, '2'为空间预约"""
        self.booking_type = booking_type
        # 重置相关数据，避免使用旧的预约类型数据
        self.booking_space = None
        self.booking_time = None
        return True

    def get_premises(self):
        """获取场馆列表"""
        if not self.is_logged_in:
            return []
            
        try:
            booking_select_time_url = 'http://booking.lib.zju.edu.cn/reserve/index/quickSelect'
            
            # 根据预约类型使用不同的参数
            booking_select_time_data = {
                'id': self.booking_type,  # '1'为座位预约, '2'为空间预约
                'date': formatted_date,
                'authorization': self.booking_user_authorization,
            }
            
            booking_select_time_resp = self.session.post(booking_select_time_url, data=booking_select_time_data)
            self.booking_select_time_data = json.loads(booking_select_time_resp.text)
            
            # 获取场馆列表
            premises_list = [(i['id'], i['name']) for i in self.booking_select_time_data['data']['premises']]
            return [f"{id} - {name}" for id, name in premises_list]
        except Exception as e:
            print(f"获取场馆失败: {str(e)}")
            return []

    def get_storeys(self, premises_id):
        """获取楼层列表"""
        if not self.is_logged_in or not self.booking_select_time_data:
            return []
            
        try:
            # 获取楼层信息
            storey_list = []
            for i in self.booking_select_time_data['data']['storey']:
                if i['topId'] == premises_id:
                    storey_list.append(f"{i['id']} - {i['name']}")
            return storey_list
        except Exception as e:
            print(f"获取楼层失败: {str(e)}")
            return []

    def get_areas(self, storey_id):
        """获取区域列表"""
        if not self.is_logged_in or not self.booking_select_time_data:
            return []
            
        try:
            # 获取区域信息
            area_list = []
            for i in self.booking_select_time_data['data']['area']:
                if i['parentId'] == storey_id:
                    area_list.append(f"{i['id']} - {i['name']}")
            return area_list
        except Exception as e:
            print(f"获取区域失败: {str(e)}")
            return []

    def get_seats(self, area_id):
        """获取座位列表 - 仅用于座位预约模式"""
        if not self.is_logged_in or self.booking_type != '1':
            return [], []
            
        try:
            # 获取座位信息
            booking_seat_url = 'http://booking.lib.zju.edu.cn/api/Seat/seat'
            booking_date_url = 'http://booking.lib.zju.edu.cn/api/Seat/date'
            
            # 获取日期信息
            booking_date_data = {
                'build_id': area_id,
                'authorization': self.booking_user_authorization,
            }
            booking_date_resp = self.session.post(booking_date_url, data=booking_date_data)
            self.booking_date_ = json.loads(booking_date_resp.text)
            
            # 获取座位信息
            booking_seat_data = {
                'area': area_id,
                'segment': self.booking_date_['data'][0]['times'][0]['id'],
                'day': self.booking_date_['data'][0]['day'],
                'startTime': self.booking_date_['data'][0]['times'][0]['start'],
                'endTime': self.booking_date_['data'][0]['times'][0]['end'],
                'authorization': self.booking_user_authorization,
            }
            
            booking_seat_resp = self.session.post(booking_seat_url, data=booking_seat_data)
            self.booking_seat = json.loads(booking_seat_resp.text)
            
            # 处理座位状态
            free_seats = []  # 空闲座位列表
            all_seats = []   # 所有座位列表
            
            # 统计空闲和使用中的座位
            free_num = 0
            using_num = 0
            
            for seat in self.booking_seat['data']:
                seat_id = seat['id']
                seat_name = seat['name']
                seat_status = seat['status']
                
                # 判断状态
                if seat_status == '1':
                    status_text = '空闲'
                    free_num += 1
                    # 只添加空闲座位到下拉选择框
                    free_seats.append(f"{seat_id} - {seat_name}")
                elif seat_status in ['6', '7']:
                    status_text = '已占用'
                    using_num += 1
                else:
                    status_text = '不可用'
                
                # 添加到所有座位列表(用于树状视图)
                all_seats.append((seat_id, seat_name, status_text))
            
            print(f"总座位数: {len(all_seats)}, 空闲座位数: {free_num}, 已占用座位数: {using_num}")
            
            return free_seats, all_seats
        except Exception as e:
            print(f"获取座位失败: {str(e)}")
            return [], []

    def confirm_booking(self, seat_id, day, start_time, end_time):
        """确认座位预约 - 仅用于座位预约模式"""
        if not self.is_logged_in or self.booking_type != '1':
            return False, "未登录或不是座位预约模式"
            
        try:
            date = datetime.now().strftime("%Y%m%d")
            booking_confirm_url = 'http://booking.lib.zju.edu.cn/api/Seat/confirm'

            # AES加密
            key = str(date+date[::-1])
            key = key[:16].encode('utf-8')
            iv = b'ZZWBKJ_ZHIHUAWEI'
            
            # 加密座位预约数据
            segment = self.booking_date_['data'][0]['times'][0]['id']
            plaintext = f'{{"seat_id":"{seat_id}","segment":"{segment}"}}'
            
            cipher = AES.new(key, AES.MODE_CBC, iv)
            padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            encrypted_text = base64.b64encode(encrypted_data).decode('utf-8')
            
            # 创建预约请求
            booking_confirm_data = {
                'aesjson': encrypted_text,                
                'authorization': self.booking_user_authorization,
            }
            
            # 添加headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'authorization': self.booking_user_authorization
            }
            
            booking_confirm_resp = self.session.post(booking_confirm_url, headers=headers, data=booking_confirm_data)
            booking_confirm_result = json.loads(booking_confirm_resp.text)
            
            if booking_confirm_result.get('code') == 1:
                return True, booking_confirm_result.get('msg', "预约成功（程序）")
            else:
                return False, booking_confirm_result.get('msg', "预约失败（程序）")

        except Exception as e:
            print(f"预约座位失败: {str(e)}")
            return False, f"预约失败: {str(e)}"
            
    # ===== 空间预约相关方法 =====
    def get_space_detail(self, room_id):
        """获取空间详情 - 用于空间预约模式"""
        if not self.is_logged_in or self.booking_type != '2':
            return None

        try:
            # 获取空间详情API - 使用正确的API端点
            space_detail_url = 'http://booking.lib.zju.edu.cn/api/Seminar/detail'

            # 准备请求参数 - 使用JSON格式，参照.har文件
            space_detail_data = {
                'id': room_id,
                'day': datetime.now().strftime("%Y-%m-%d"),
                'authorization': self.booking_user_authorization,
            }

            headers = {
                'Content-Type': 'application/json',
                'authorization': self.booking_user_authorization
            }

            space_detail_resp = self.session.post(space_detail_url, json=space_detail_data, headers=headers, verify=False)
            space_detail = json.loads(space_detail_resp.text)

            print(f"空间详情API响应: {space_detail}")  # 调试信息

            titles = []
            if space_detail.get('code') == 1 and 'data' in space_detail:
                # 根据.har文件，空间详情包含children信息
                if 'children' in space_detail['data']:
                    for child in space_detail['data']['children']:
                        titles.append([child['id'], child['name']])
                else:
                    # 如果没有children，提供默认标题
                    titles = [['1', '学习'], ['2', '研讨'], ['3', '会议']]
            else:
                # 提供默认标题
                titles = [['1', '学习'], ['2', '研讨'], ['3', '会议']]

            return {'titles': titles}
        except Exception as e:
            print(f"获取空间详情失败: {str(e)}")
            # 返回默认标题
            return {'titles': [['1', '学习'], ['2', '研讨'], ['3', '会议']]}
    
    def get_space_time_slots(self, premise_id, area_id):
        """获取空间可用时间段 - 用于空间预约模式"""
        if not self.is_logged_in or self.booking_type != '2':
            return []

        try:
            # 获取空间时间段API - 使用v1seminar端点，参照.har文件
            booking_time_url = 'http://booking.lib.zju.edu.cn/api/Seminar/v1seminar'

            # 准备请求参数 - 使用JSON格式，参照.har文件
            booking_time_data = {
                'room': area_id,  # room参数使用area_id
                'area': premise_id,  # area参数使用premise_id
                'authorization': self.booking_user_authorization,
            }

            headers = {
                'Content-Type': 'application/json',
                'authorization': self.booking_user_authorization
            }

            booking_time_resp = self.session.post(booking_time_url, json=booking_time_data, headers=headers, verify=False)
            self.booking_time = json.loads(booking_time_resp.text)

            print(f"空间时间段API响应: {self.booking_time}")  # 调试信息

            # 检查响应格式并提取可用的时间段
            date_slots = []
            if 'data' in self.booking_time and 'list' in self.booking_time['data']:
                for date_info in self.booking_time["data"]["list"]:
                    date = date_info["date"]
                    start_time = date_info["info"]["startTimeStamp"][-8:-3]
                    end_time = date_info["info"]["endTimeStamp"][-8:-3]
                    date_slots.append({
                        'date': date,
                        'display': date if date_info["info"]["Fully_Booked"] == '0' else f"{date}(已满)",
                        'start_time': start_time,
                        'end_time': end_time
                    })
            elif 'data' in self.booking_time and 'date' in self.booking_time['data']:
                # 处理另一种响应格式
                for date in self.booking_time['data']['date']:
                    date_slots.append({
                        'date': date,
                        'display': date,
                        'start_time': '08:30',
                        'end_time': '22:30'
                    })

            return date_slots
        except Exception as e:
            print(f"获取时间段失败: {str(e)}")
            return []
    
    def confirm_space_booking(self, room_id, day, start_time, end_time, title, title_id):
        """确认空间预约 - 用于空间预约模式"""
        if not self.is_logged_in or self.booking_type != '2':
            return False, "未登录或不是空间预约模式"

        try:
            date = datetime.now().strftime("%Y%m%d")
            booking_confirm_url = 'http://booking.lib.zju.edu.cn/reserve/index/confirm'

            # AES加密 - 修正密钥长度
            key = str(date+date[::-1])
            key = key[:16].encode('utf-8')  # 确保密钥长度为16字节
            iv = b'ZZWBKJ_ZHIHUAWEI'

            # 加密预约数据 - 修正JSON格式
            plaintext = f'{{"day":"{day}","start_time":"{start_time}","end_time":"{end_time}","title":"{title}","content":"1","mobile":"13357008040","room":"{room_id}","open":"1","file_name":"","file_url":"","titleId":"{title_id}","id":"2"}}'

            print(f"空间预约加密前数据: {plaintext}")  # 调试信息

            cipher = AES.new(key, AES.MODE_CBC, iv)
            padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            encrypted_text = base64.b64encode(encrypted_data).decode('utf-8')

            # 创建预约请求
            booking_confirm_data = {
                'aesjson': encrypted_text,
                'authorization': self.booking_user_authorization,
            }

            # 添加headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Content-Type': 'application/json',
                'authorization': self.booking_user_authorization
            }

            # 发送JSON格式的请求
            booking_confirm_resp = self.session.post(booking_confirm_url, headers=headers, json=booking_confirm_data)
            booking_confirm_result = json.loads(booking_confirm_resp.text)

            print(f"空间预约API响应: {booking_confirm_result}")  # 调试信息

            if booking_confirm_result.get('code') == 1:
                return True, booking_confirm_result.get('msg', "预约成功（程序）")
            else:
                return False, booking_confirm_result.get('msg', "预约失败（程序）")
        except Exception as e:
            print(f"预约空间失败: {str(e)}")
            return False, f"预约失败: {str(e)}"
            
    def get_fifth_floor_rooms(self):
        """获取五层空间信息"""
        if not self.is_logged_in:
            return []
            
        try:
            booking_select_time_url = 'http://booking.lib.zju.edu.cn/reserve/index/quickSelect'
            
            # i = 1
            rooms = []
            for date in (formatted_date, formatted_tomorrow_date):
                booking_select_time_data = {
                    'id': '2',  # '1'为座位预约, '2'为空间预约
                    'date': date,
                    'authorization': self.booking_user_authorization,
                    'members': 0
                }

                # booking_select_time_resp = self.session.post(booking_select_time_url + str(i), data=booking_select_time_data)
                booking_select_time_resp = self.session.post(booking_select_time_url, data=booking_select_time_data)
                booking_select_time_data = json.loads(booking_select_time_resp.text)

                for room in booking_select_time_data['data']['area']:
                    # 检查是否为五层空间（parentId为五层的ID）
                    if room['parentId'] == '57':  # 五层的正确ID是57
                        room_id = room['id']
                        room_name = room['name']
                        # 检查是否包含5SC字样（五层空间的特征）
                        if '5SC' in room_name:
                            room_status = (room['Fully_Booked'] == '0')  # 空闲
                            rooms.append({
                                'id': room_id,
                                'name': room_name,
                                'date': date,
                               'status': room_status})
                # i += 1
            return rooms

        except Exception as e:
            print(f"获取五层空间信息失败: {str(e)}")
            return []