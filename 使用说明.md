# 浙大图书馆预约系统 - 使用说明

## 修复完成

✅ **GUI文字遮挡问题已修复**
✅ **空间预约功能已修复**
✅ **代码质量已提升**

## 运行方法

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
python src/main.py
```

## 主要功能

### 1. 座位预约
- 选择场馆、楼层、区域、座位
- 支持定时预约功能
- 实时显示座位状态

### 2. 空间预约（已修复）
- 选择场馆、楼层、空间
- 选择预约标题和时间段
- 支持多种空间类型预约

### 3. 五层状态监控
- 实时显示五层-5SCXX空间状态
- 自动统计空闲/占用情况
- 支持手动刷新

## 修复内容

### GUI优化
- 窗口尺寸从1000x1000增大到1200x1100
- 优化空间预约界面布局和间距
- 修复文字遮挡问题

### 功能修复
- 修正空间预约API端点
- 修复AES加密参数问题
- 改进错误处理机制
- 增加调试信息输出

### 代码质量
- 清理重复代码
- 修正依赖配置
- 增强异常处理

## 注意事项

1. **网络连接**：需要能够访问浙大图书馆预约系统
2. **登录信息**：使用统一身份认证账号密码
3. **调试信息**：程序会输出详细的调试信息，便于问题排查

## 测试验证

运行测试脚本验证修复效果：
```bash
python final_test.py
```

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 登录信息是否正确
3. 查看程序输出的调试信息

修复完成，可以正常使用！🎉
