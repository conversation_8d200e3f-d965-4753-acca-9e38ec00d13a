import tkinter as tk
from tkinter import ttk

def create_label(frame, text, row, column):
    label = ttk.Label(frame, text=text)
    label.grid(row=row, column=column, sticky=tk.W)

def create_entry(frame, textvariable, row, column, show=None):
    entry = ttk.Entry(frame, textvariable=textvariable, show=show)
    entry.grid(row=row, column=column, padx=5, pady=5)

def create_button(frame, text, command, row, column, colspan=1):
    button = ttk.Button(frame, text=text, command=command)
    button.grid(row=row, column=column, columnspan=colspan, pady=10)

def create_combobox(frame, textvariable, row, column, values):
    combo = ttk.Combobox(frame, textvariable=textvariable)
    combo['values'] = values
    combo.grid(row=row, column=column, padx=5, pady=5)
    return combo