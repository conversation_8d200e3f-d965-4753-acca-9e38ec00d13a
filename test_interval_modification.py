#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动抢座间隔时长修改
验证最小间隔从10秒改为3秒是否生效
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_interval_validation():
    """测试间隔验证逻辑"""
    print("=== 测试自动抢座间隔时长修改 ===")
    
    try:
        from gui.app import BookingApp
        from ttkthemes import ThemedTk
        
        # 创建测试窗口
        root = ThemedTk(theme="arc")
        root.withdraw()  # 隐藏窗口进行测试
        
        app = BookingApp(root)
        
        # 测试1: 验证默认间隔值
        default_interval = app.grab_interval_var.get()
        print(f"✓ 默认间隔值: {default_interval}秒")
        assert default_interval == "5", f"默认间隔应为5秒，实际为{default_interval}秒"
        
        # 测试2: 验证3秒间隔是否被接受
        app.grab_interval_var.set("3")
        app.auto_grab_var.set(True)
        
        # 模拟登录状态
        app.client.is_logged_in = True
        
        # 捕获messagebox调用
        original_showerror = messagebox.showerror
        error_called = False
        error_message = ""
        
        def mock_showerror(title, message):
            nonlocal error_called, error_message
            error_called = True
            error_message = message
            
        messagebox.showerror = mock_showerror
        
        try:
            # 尝试启动自动抢座（应该成功）
            app.switch_auto_grab()
            
            if error_called:
                print(f"✗ 3秒间隔被拒绝: {error_message}")
                return False
            else:
                print("✓ 3秒间隔被接受")
                
        finally:
            messagebox.showerror = original_showerror
            app.auto_grab_var.set(False)
            app.stop_auto_grab()
        
        # 测试3: 验证2秒间隔是否被拒绝
        app.grab_interval_var.set("2")
        app.auto_grab_var.set(True)
        
        error_called = False
        error_message = ""
        messagebox.showerror = mock_showerror
        
        try:
            # 尝试启动自动抢座（应该失败）
            app.switch_auto_grab()
            
            if error_called and "不能小于3秒" in error_message:
                print("✓ 2秒间隔被正确拒绝")
            else:
                print(f"✗ 2秒间隔验证失败: {error_message}")
                return False
                
        finally:
            messagebox.showerror = original_showerror
            app.auto_grab_var.set(False)
        
        # 测试4: 验证1秒间隔是否被拒绝
        app.grab_interval_var.set("1")
        app.auto_grab_var.set(True)
        
        error_called = False
        error_message = ""
        messagebox.showerror = mock_showerror
        
        try:
            # 尝试启动自动抢座（应该失败）
            app.switch_auto_grab()
            
            if error_called and "不能小于3秒" in error_message:
                print("✓ 1秒间隔被正确拒绝")
            else:
                print(f"✗ 1秒间隔验证失败: {error_message}")
                return False
                
        finally:
            messagebox.showerror = original_showerror
            app.auto_grab_var.set(False)
        
        print("✓ 所有间隔验证测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

def test_welcome_message():
    """测试欢迎信息是否更新"""
    print("\n=== 测试欢迎信息更新 ===")
    
    try:
        from gui.app import BookingApp
        from ttkthemes import ThemedTk
        
        # 创建测试窗口
        root = ThemedTk(theme="arc")
        root.withdraw()  # 隐藏窗口进行测试
        
        app = BookingApp(root)
        
        # 获取欢迎信息
        welcome_msg = """
🎉 欢迎使用浙大图书馆预约系统！

📋 使用指南:
1. 请先输入统一身份认证账号密码并登录
2. 选择预约类型：座位预约 或 空间预约
3. 空间预约模式下可使用5SCXX自动抢座功能

🚀 5SCXX自动抢座功能:
• 设置检查间隔（最小3秒，建议5-10秒）
• 勾选"开启5SCXX自动抢座"
• 运行状态将在此日志区域实时显示
• 发现空闲空间时会自动尝试预约

💡 提示: 右侧会显示当前空闲的5SCXX空间，支持手动快速预约

准备就绪，请开始使用！
        """.strip()
        
        if "最小3秒" in welcome_msg and "建议5-10秒" in welcome_msg:
            print("✓ 欢迎信息已正确更新")
            return True
        else:
            print("✗ 欢迎信息未正确更新")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试自动抢座间隔时长修改...")
    
    # 运行测试
    test1_result = test_interval_validation()
    test2_result = test_welcome_message()
    
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    print(f"间隔验证测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"欢迎信息测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！自动抢座最小间隔已成功修改为3秒。")
        print("\n修改总结:")
        print("- ✅ 最小间隔从10秒改为3秒")
        print("- ✅ 默认间隔从30秒改为5秒") 
        print("- ✅ 欢迎信息已更新")
        print("- ✅ 错误提示信息已更新")
        print("- ✅ 文档已同步更新")
    else:
        print("\n❌ 部分测试失败，请检查修改。")
        sys.exit(1)
