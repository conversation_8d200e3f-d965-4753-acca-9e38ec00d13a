# 浙大图书馆座位预约系统

## 项目简介
该项目是一个用于浙大图书馆座位预约的系统，用户可以通过该系统登录并预约座位。

## 项目结构
```
zju_lib_booking_system
├── src
│   ├── __init__.py
│   ├── main.py
│   ├── gui
│   │   ├── __init__.py
│   │   ├── app.py
│   │   └── components.py
│   ├── api
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── endpoints.py
│   ├── utils
│   │   ├── __init__.py
│   │   ├── encryption.py
│   │   └── logger.py
│   └── config
│       ├── __init__.py
│       └── settings.py
├── requirements.txt
└── README.md
```

## 安装依赖
使用以下命令安装项目所需的依赖：
```
pip install -r requirements.txt
```

## 运行项目
使用以下命令运行项目：
```
python src/main.py
```

## 功能
- 用户登录
- 选择场馆、楼层、区域和座位
- 确认预约座位
- 日志记录功能

## 贡献
欢迎任何形式的贡献，您可以通过提交问题或拉取请求来参与项目。