import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from api.client import APIClient
from utils.logger import log_message
from datetime import datetime, timedelta
from datetime import time as dtime
import threading
import time

class BookingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("浙大图书馆预约系统")
        self.root.geometry("1200x1100")  # 进一步增大窗口尺寸以避免文字遮挡
        self.style = ttk.Style()
        self.style.configure("TFrame", background="#f0f0f0")
        self.style.configure("TLabelframe", background="#f0f0f0")
        self.style.configure("TLabelframe.Label", font=('微软雅黑', 10))
        self.style.configure("TLabel", font=('微软雅黑', 10))
        self.style.configure("TButton", font=('微软雅黑', 10))
        self.style.configure("Treeview", font=('微软雅黑', 9), rowheight=25)

        self.booking_type = '1'  # 默认为座位预约类型
        self.timer_time = None
        self.timer_on = False
        self.timer_thread = None

        # 自动抢座相关变量
        self.auto_grab_on = False
        self.auto_grab_thread = None
        
        # 创建API客户端实例
        self.client = APIClient()
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 登录框架
        self.login_frame = ttk.LabelFrame(self.main_frame, text="登录", padding="5")
        self.login_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 用户名输入
        ttk.Label(self.login_frame, text="统一身份认证用户名：").grid(row=0, column=0, sticky=tk.W)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(self.login_frame, textvariable=self.username_var)
        self.username_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 密码输入
        ttk.Label(self.login_frame, text="统一身份认证密码：").grid(row=1, column=0, sticky=tk.W)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(self.login_frame, textvariable=self.password_var, show="*")
        self.password_entry.grid(row=1, column=1, padx=5, pady=5)
        
        # 登录按钮
        self.login_btn = ttk.Button(self.login_frame, text="登录", command=self.login)
        self.login_btn.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 新增: 五层-5SCXX状态显示框架 (右上角)
        self.fifth_floor_frame = ttk.LabelFrame(self.main_frame, text="五层-5SCXX状态监控", padding="5")
        self.fifth_floor_frame.grid(row=0, column=1, rowspan=2, sticky=(tk.N, tk.S, tk.E, tk.W), padx=5)
        
        # 添加刷新按钮
        self.refresh_fifth_floor_btn = ttk.Button(self.fifth_floor_frame, text="刷新五层状态", command=self.refresh_fifth_floor_status)
        self.refresh_fifth_floor_btn.grid(row=0, column=0, pady=5, padx=5, sticky=tk.W)

        # 5SCXX自动抢座功能
        self.auto_grab_var = tk.BooleanVar(value=False)
        self.auto_grab_switch = ttk.Checkbutton(self.fifth_floor_frame, text="开启5SCXX自动抢座",
                                               variable=self.auto_grab_var, command=self.switch_auto_grab)
        self.auto_grab_switch.grid(row=0, column=1, pady=5, padx=5, sticky=tk.W)

        # 抢座间隔设置
        ttk.Label(self.fifth_floor_frame, text="检查间隔(秒)：").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.grab_interval_var = tk.StringVar(value="5")
        self.grab_interval_entry = ttk.Entry(self.fifth_floor_frame, textvariable=self.grab_interval_var, width=8)
        self.grab_interval_entry.grid(row=0, column=3, padx=5, pady=5)

        # 自动抢座状态显示（简化版本）
        self.grab_status_var = tk.StringVar(value="未启动")
        self.grab_status_label = ttk.Label(self.fifth_floor_frame, textvariable=self.grab_status_var,
                                          foreground="blue", font=('微软雅黑', 9, 'bold'))
        self.grab_status_label.grid(row=0, column=4, padx=5, pady=5, sticky=tk.W)
        
        # 创建树形视图显示五层-5SCXX状态
        self.fifth_floor_tree = ttk.Treeview(self.fifth_floor_frame, columns=('id', 'name', 'date', 'status'), show='headings', height=10)
        self.fifth_floor_tree.heading('id', text='空间ID')
        self.fifth_floor_tree.heading('name', text='空间名称')
        self.fifth_floor_tree.heading('date', text='日期')
        self.fifth_floor_tree.heading('status', text='状态')
        self.fifth_floor_tree.column('id', width=80)
        self.fifth_floor_tree.column('name', width=150)
        self.fifth_floor_tree.column('date', width=100)
        self.fifth_floor_tree.column('status', width=80)
        self.fifth_floor_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加滚动条
        fifth_floor_scrollbar = ttk.Scrollbar(self.fifth_floor_frame, orient=tk.VERTICAL, command=self.fifth_floor_tree.yview)
        fifth_floor_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.fifth_floor_tree.configure(yscrollcommand=fifth_floor_scrollbar.set)
        
        # 五层状态统计
        self.fifth_floor_stats_frame = ttk.Frame(self.fifth_floor_frame, padding="5")
        self.fifth_floor_stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(self.fifth_floor_stats_frame, text="总空间数:").grid(row=0, column=0, sticky=tk.W)
        self.total_seats_var = tk.StringVar(value="0")
        ttk.Label(self.fifth_floor_stats_frame, textvariable=self.total_seats_var).grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(self.fifth_floor_stats_frame, text="空闲空间:").grid(row=1, column=0, sticky=tk.W)
        self.free_seats_var = tk.StringVar(value="0")
        ttk.Label(self.fifth_floor_stats_frame, textvariable=self.free_seats_var).grid(row=1, column=1, sticky=tk.W)
        
        ttk.Label(self.fifth_floor_stats_frame, text="已占空间:").grid(row=2, column=0, sticky=tk.W)
        self.used_seats_var = tk.StringVar(value="0")
        ttk.Label(self.fifth_floor_stats_frame, textvariable=self.used_seats_var).grid(row=2, column=1, sticky=tk.W)
        
        # 预约类型选择框架
        self.booking_type_frame = ttk.LabelFrame(self.main_frame, text="预约类型", padding="5")
        self.booking_type_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.booking_type_frame.grid_remove()  # 初始隐藏
        
        # 预约类型选择按钮
        self.seat_btn = ttk.Button(self.booking_type_frame, text="座位预约", command=lambda: self.switch_booking_type('1'))
        self.seat_btn.grid(row=0, column=0, padx=5, pady=5)
        
        self.space_btn = ttk.Button(self.booking_type_frame, text="空间预约", command=lambda: self.switch_booking_type('2'))
        self.space_btn.grid(row=0, column=1, padx=5, pady=5)

        # 定时预约框架
        self.booking_timer_frame = ttk.LabelFrame(self.main_frame, text="定时预约", padding="5")
        self.booking_timer_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.booking_timer_frame.grid_remove()  # 初始隐藏

        # 定时预约
        ttk.Label(self.booking_timer_frame, text="时间：").grid(row=0, column=0, sticky=tk.W)
        self.timer_var = tk.StringVar()
        self.timer_entry = ttk.Entry(self.booking_timer_frame, textvariable=self.timer_var)
        self.timer_entry.grid(row=0, column=1, padx=5, pady=5)

        # 定时预约开关
        self.timer_switch_var = tk.BooleanVar(value=False)
        self.timer_switch = ttk.Checkbutton(self.booking_timer_frame, text="开启定时预约", variable=self.timer_switch_var, command=self.switch_timer)
        self.timer_switch.grid(row=1, column=0, columnspan=2, pady=5)
        
        # 预约选择框架
        self.booking_frame = ttk.LabelFrame(self.main_frame, text="座位预约信息", padding="5")
        self.booking_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        self.booking_frame.grid_remove()  # 初始隐藏
        
        # 空间预约框架
        self.space_booking_frame = ttk.LabelFrame(self.main_frame, text="空间预约信息", padding="10")
        self.space_booking_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=5)
        self.space_booking_frame.grid_remove()  # 初始隐藏
        
        # 右侧信息显示区域 - 重新设计为上下分布
        self.info_container = ttk.Frame(self.main_frame)
        self.info_container.grid(row=2, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5)

        # 1. 座位/空间信息显示框架（顶部）
        self.seats_frame = ttk.LabelFrame(self.info_container, text="可预约信息", padding="5")
        self.seats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        self.seats_frame.grid_remove()  # 初始隐藏

        # 创建树形视图显示座位/空间信息（缩小高度）
        self.seats_tree = ttk.Treeview(self.seats_frame, columns=('id', 'name', 'status'), show='headings', height=3)
        self.seats_tree.heading('id', text='ID')
        self.seats_tree.heading('name', text='名称')
        self.seats_tree.heading('status', text='状态')
        self.seats_tree.column('id', width=80)
        self.seats_tree.column('name', width=120)
        self.seats_tree.column('status', width=80)
        self.seats_tree.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 添加滚动条
        seats_scrollbar = ttk.Scrollbar(self.seats_frame, orient=tk.VERTICAL, command=self.seats_tree.yview)
        seats_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.seats_tree.configure(yscrollcommand=seats_scrollbar.set)

        # 2. 当前空闲5SCXX显示区（中部）
        self.available_5scxx_frame = ttk.LabelFrame(self.info_container, text="当前空闲5SCXX空间", padding="5")
        self.available_5scxx_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        self.available_5scxx_frame.grid_remove()  # 初始隐藏，只在空间预约模式显示

        # 空闲5SCXX列表
        self.available_5scxx_listbox = tk.Listbox(self.available_5scxx_frame, height=4, font=('微软雅黑', 9))
        self.available_5scxx_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 空闲5SCXX滚动条
        available_scrollbar = ttk.Scrollbar(self.available_5scxx_frame, orient="vertical", command=self.available_5scxx_listbox.yview)
        available_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.available_5scxx_listbox.configure(yscrollcommand=available_scrollbar.set)

        # 空闲5SCXX操作按钮
        self.available_5scxx_btn_frame = ttk.Frame(self.available_5scxx_frame)
        self.available_5scxx_btn_frame.grid(row=1, column=0, columnspan=2, pady=5)

        self.refresh_available_btn = ttk.Button(self.available_5scxx_btn_frame, text="刷新空闲列表",
                                               command=self.refresh_available_5scxx)
        self.refresh_available_btn.grid(row=0, column=0, padx=5)

        self.quick_grab_btn = ttk.Button(self.available_5scxx_btn_frame, text="快速预约选中",
                                        command=self.quick_grab_selected)
        self.quick_grab_btn.grid(row=0, column=1, padx=5)

        # 配置框架权重
        self.info_container.columnconfigure(0, weight=1)
        self.seats_frame.columnconfigure(0, weight=1)
        self.available_5scxx_frame.columnconfigure(0, weight=1)
        
        # ===== 座位预约界面 =====
        # 场馆选择
        self.premises_var = tk.StringVar()
        ttk.Label(self.booking_frame, text="选择场馆：").grid(row=0, column=0, sticky=tk.W)
        self.premises_combo = ttk.Combobox(self.booking_frame, textvariable=self.premises_var)
        self.premises_combo.grid(row=0, column=1, padx=5, pady=5)
        self.premises_combo.bind('<<ComboboxSelected>>', self.on_premises_select)
        
        # 楼层选择
        self.storey_var = tk.StringVar()
        ttk.Label(self.booking_frame, text="选择楼层：").grid(row=1, column=0, sticky=tk.W)
        self.storey_combo = ttk.Combobox(self.booking_frame, textvariable=self.storey_var)
        self.storey_combo.grid(row=1, column=1, padx=5, pady=5)
        self.storey_combo.bind('<<ComboboxSelected>>', self.on_storey_select)
        
        # 区域选择
        self.area_var = tk.StringVar()
        ttk.Label(self.booking_frame, text="选择区域：").grid(row=2, column=0, sticky=tk.W)
        self.area_combo = ttk.Combobox(self.booking_frame, textvariable=self.area_var)
        self.area_combo.grid(row=2, column=1, padx=5, pady=5)
        self.area_combo.bind('<<ComboboxSelected>>', self.on_area_select)
        
        # 座位选择
        self.seat_var = tk.StringVar()
        ttk.Label(self.booking_frame, text="选择座位：").grid(row=3, column=0, sticky=tk.W)
        self.seat_combo = ttk.Combobox(self.booking_frame, textvariable=self.seat_var)
        self.seat_combo.grid(row=3, column=1, padx=5, pady=5)
        
        # 预约按钮
        self.book_btn = ttk.Button(self.booking_frame, text="预约", command=self.confirm_booking)
        self.book_btn.grid(row=4, column=0, columnspan=2, pady=10)
        
        # ===== 空间预约界面 =====
        # 场馆选择
        self.space_premises_var = tk.StringVar()
        ttk.Label(self.space_booking_frame, text="选择场馆：").grid(row=0, column=0, sticky=tk.W, padx=5, pady=3)
        self.space_premises_combo = ttk.Combobox(self.space_booking_frame, textvariable=self.space_premises_var, width=25)
        self.space_premises_combo.grid(row=0, column=1, padx=5, pady=3, sticky=(tk.W, tk.E))
        self.space_premises_combo.bind('<<ComboboxSelected>>', self.on_space_premises_select)

        # 楼层选择
        self.space_storey_var = tk.StringVar()
        ttk.Label(self.space_booking_frame, text="选择楼层：").grid(row=1, column=0, sticky=tk.W, padx=5, pady=3)
        self.space_storey_combo = ttk.Combobox(self.space_booking_frame, textvariable=self.space_storey_var, width=25)
        self.space_storey_combo.grid(row=1, column=1, padx=5, pady=3, sticky=(tk.W, tk.E))
        self.space_storey_combo.bind('<<ComboboxSelected>>', self.on_space_storey_select)

        # 空间选择
        self.space_var = tk.StringVar()
        ttk.Label(self.space_booking_frame, text="选择空间：").grid(row=2, column=0, sticky=tk.W, padx=5, pady=3)
        self.space_combo = ttk.Combobox(self.space_booking_frame, textvariable=self.space_var, width=25)
        self.space_combo.grid(row=2, column=1, padx=5, pady=3, sticky=(tk.W, tk.E))
        self.space_combo.bind('<<ComboboxSelected>>', self.on_space_select)

        # 标题选择
        self.title_var = tk.StringVar()
        ttk.Label(self.space_booking_frame, text="选择标题：").grid(row=3, column=0, sticky=tk.W, padx=5, pady=3)
        self.title_combo = ttk.Combobox(self.space_booking_frame, textvariable=self.title_var, width=25)
        self.title_combo.grid(row=3, column=1, padx=5, pady=3, sticky=(tk.W, tk.E))

        # 日期选择
        self.time_slot_var = tk.StringVar()
        ttk.Label(self.space_booking_frame, text="选择日期：").grid(row=4, column=0, sticky=tk.W, padx=5, pady=3)
        self.time_slot_combo = ttk.Combobox(self.space_booking_frame, textvariable=self.time_slot_var, width=25)
        self.time_slot_combo.grid(row=4, column=1, padx=5, pady=3, sticky=(tk.W, tk.E))

        # 预约按钮
        self.space_book_btn = ttk.Button(self.space_booking_frame, text="预约", command=self.confirm_space_booking)
        self.space_book_btn.grid(row=5, column=0, columnspan=2, pady=10)

        # 配置空间预约框架的列权重
        self.space_booking_frame.columnconfigure(1, weight=1)
        
        # 信息显示区
        self.log_text = scrolledtext.ScrolledText(self.main_frame, height=15)
        self.log_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 配置行列权重，使UI可伸缩
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(0, weight=2)
        self.main_frame.rowconfigure(1, weight=1)
        self.main_frame.rowconfigure(2, weight=1)
        self.main_frame.rowconfigure(3, weight=2)
        self.main_frame.rowconfigure(5, weight=1)  # 日志区域

        # 右侧信息容器权重配置
        self.info_container.columnconfigure(0, weight=1)
        self.info_container.rowconfigure(0, weight=0)  # 选中信息区域固定高度
        self.info_container.rowconfigure(1, weight=0)  # 空闲5SCXX区域固定高度

        # 各框架内部权重配置
        self.seats_frame.columnconfigure(0, weight=1)
        self.available_5scxx_frame.columnconfigure(0, weight=1)
        self.fifth_floor_frame.columnconfigure(0, weight=1)
        self.fifth_floor_frame.rowconfigure(1, weight=1)

        # 五层-5SCXX的配置参数 (可以根据实际情况修改)
        self.fifth_floor_config = {
            "premises_id": "1",   # 主馆ID
            "storey_id": "15",    # 五层ID
            "area_id": "93"       # 5SCXX区域ID
        }

        # 显示初始化提示
        self.show_welcome_message()

    def show_welcome_message(self):
        """显示欢迎和使用提示"""
        welcome_msg = """
🎉 欢迎使用浙大图书馆预约系统！

📋 使用指南:
1. 请先输入统一身份认证账号密码并登录
2. 选择预约类型：座位预约 或 空间预约
3. 空间预约模式下可使用5SCXX自动抢座功能

🚀 5SCXX自动抢座功能:
• 设置检查间隔（最小3秒，建议5-10秒）
• 勾选"开启5SCXX自动抢座"
• 运行状态将在此日志区域实时显示
• 发现空闲空间时会自动尝试预约

💡 提示: 右侧会显示当前空闲的5SCXX空间，支持手动快速预约

准备就绪，请开始使用！
        """.strip()
        self.log_message(welcome_msg)

    def log_message(self, message):
        log_message(message)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def clear_tree(self):
        for item in self.seats_tree.get_children():
            self.seats_tree.delete(item)
    
    # refresh_fifth_floor_status方法的完整修改版本

    def refresh_fifth_floor_status(self):
        """刷新五层-5SCXX的空间状态"""
        # 清空树形视图
        for item in self.fifth_floor_tree.get_children():
            self.fifth_floor_tree.delete(item)
            
        # 检查登录状态
        if not self.client.is_logged_in:
            self.log_message("请先登录后刷新五层状态")
            return
            
        self.log_message("正在获取五层-5SCXX空间状态...")
        
        try:
            # 临时切换到空间预约模式
            original_booking_type = self.client.booking_type
            self.client.set_booking_type('2')
            
            fifth_floor_rooms = self.client.get_fifth_floor_rooms()
            
            # 统计五层状态
            total_count = len(fifth_floor_rooms)  # 总空间数就是区域数
            available_count = 0  # 可预约的空间数
            
            # 显示五层所有空间
            for room in fifth_floor_rooms:
                room_id = room['id']
                room_name = room['name']
                room_date = room['date']
                room_status = room['status']
                if room_status:
                    status_text = '空闲'
                    available_count += 1
                else:
                    status_text = '占用'
                
                # 添加到树形视图
                self.fifth_floor_tree.insert('', 'end', values=(room_id, room_name, room_date, status_text))
            
            # 更新统计信息
            self.total_seats_var.set(str(total_count))
            self.free_seats_var.set(str(available_count))
            self.used_seats_var.set(str(total_count - available_count))
            
            # 显示五层整体信息
            is_fully_booked = "是" if available_count == 0 else "否"
            booking_percentage = round(100 * (total_count - available_count) / total_count) if total_count > 0 else 0
            self.log_message(f"五层-5SCXX状态更新完成：总空间{total_count}个，可预约{available_count}个，无法预约{total_count - available_count}个，占用率{booking_percentage}%，是否全满：{is_fully_booked}")
            
            # 恢复原始预约类型
            self.client.set_booking_type(original_booking_type)

            # 同时更新空闲5SCXX列表
            available_rooms = [room for room in fifth_floor_rooms if room['status']]
            self.update_available_5scxx_display(available_rooms)

        except Exception as e:
            self.log_message(f"获取五层-5SCXX状态失败: {str(e)}")
            # 确保在出错时也恢复原始预约类型
            self.client.set_booking_type(original_booking_type)
            
    def switch_booking_type(self, booking_type):
        """切换预约类型"""
        self.client.set_booking_type(booking_type)
        self.booking_type = booking_type

        # 清除已选择的内容
        if booking_type == '1':
            # 座位预约模式
            self.log_message("切换到座位预约模式")
            self.seats_frame.config(text="空闲座位信息")
            self.booking_frame.grid()
            self.space_booking_frame.grid_remove()

            # 隐藏空间预约相关的组件
            self.available_5scxx_frame.grid_remove()

            self.get_premises_info()
        else:
            # 空间预约模式
            self.log_message("切换到空间预约模式")
            self.seats_frame.config(text="选中空间信息")
            self.booking_frame.grid_remove()
            self.space_booking_frame.grid()

            # 显示空间预约相关的组件
            self.available_5scxx_frame.grid()

            self.get_space_premises_info()

        # 清空座位/空间信息
        self.clear_tree()

    def login(self):
        try:
            username = self.username_var.get()
            password = self.password_var.get()
            
            if not username or not password:
                messagebox.showerror("错误", "请输入用户名和密码")
                return
                
            self.log_message("正在登录...")
            
            # 使用已创建的client实例登录
            if self.client.login(username, password):
                self.log_message("登录成功")
                self.booking_type_frame.grid()  # 显示预约类型选择
                self.booking_timer_frame.grid()  # 显示预约倒计时
                self.seats_frame.grid()
                
                # 登录成功后刷新五层-5SCXX状态
                self.refresh_fifth_floor_status()
                
                # 默认显示座位预约
                self.switch_booking_type('1')
            else:
                self.log_message("登录失败，请检查用户名和密码")
        except Exception as e:
            self.log_message(f"登录失败: {str(e)}")

    # ===== 座位预约相关方法 =====
    def get_premises_info(self):
        try:
            self.log_message("正在获取场馆信息...")
            # 使用实例方法获取场馆信息
            premises_list = self.client.get_premises()
            if premises_list:
                self.premises_combo['values'] = premises_list
                self.log_message(f"获取到 {len(premises_list)} 个场馆")
            else:
                self.log_message("获取场馆信息失败")
        except Exception as e:
            self.log_message(f"获取场馆信息失败: {str(e)}")

    def on_premises_select(self, event):
        try:
            selected = self.premises_combo.get()
            if not selected:
                return
                
            premises_id = selected.split(' - ')[0]
            self.log_message(f"选择场馆: {selected}")
            
            # 使用实例方法获取楼层信息
            storey_list = self.client.get_storeys(premises_id)
            self.storey_combo['values'] = storey_list
            self.area_combo.set('')
            self.seat_combo.set('')
            
            self.log_message(f"获取到 {len(storey_list)} 个楼层")
        except Exception as e:
            self.log_message(f"选择场馆失败: {str(e)}")

    def on_storey_select(self, event):
        try:
            selected = self.storey_combo.get()
            if not selected:
                return
                
            storey_id = selected.split(' - ')[0]
            self.log_message(f"选择楼层: {selected}")
            
            # 使用实例方法获取区域信息
            area_list = self.client.get_areas(storey_id)
            self.area_combo['values'] = area_list
            self.seat_combo.set('')
            
            self.log_message(f"获取到 {len(area_list)} 个区域")
        except Exception as e:
            self.log_message(f"选择楼层失败: {str(e)}")

    def on_area_select(self, event):
        self.get_seats()

    def get_seats(self):
        try:
            selected = self.area_combo.get()
            if not selected:
                return
                
            area_id = selected.split(' - ')[0]
            self.log_message(f"选择区域: {selected}")
            
            # 使用实例方法获取座位信息
            seat_list, all_seats = self.client.get_seats(area_id)
            self.seat_combo['values'] = seat_list
            
            # 清空现有树形视图内容
            self.clear_tree()
                
            # 按状态显示所有座位
            for seat_id, seat_name, status in all_seats:
                self.seats_tree.insert('', 'end', values=(seat_id, seat_name, status))
            
            self.log_message(f"获取到 {len(seat_list)} 个空闲座位")
        except Exception as e:
            self.log_message(f"获取座位失败: {str(e)}")

    def confirm_booking(self):
        try:
            if not self.seat_var.get():
                messagebox.showerror("错误", "请选择座位")
                return False
                
            seat_id = self.seat_var.get().split(' - ')[0]
            self.log_message(f"正在预约座位: {self.seat_var.get()}")
            
            # 确认预约座位
            booking_date = self.client.booking_date_['data'][0]['day']
            start_time = self.client.booking_date_['data'][0]['times'][0]['start']
            end_time = self.client.booking_date_['data'][0]['times'][0]['end']
            
            success, msg = self.client.confirm_booking(seat_id, booking_date, start_time, end_time)
            
            if success:
                messagebox.showinfo("成功", "座位预约成功！")
                self.log_message(f"预约成功: {self.seat_var.get()}")
                # 预约成功后刷新五层-5SCXX状态
                # self.refresh_fifth_floor_status()
                return True
            else:
                messagebox.showerror("错误", f"预约失败: {msg}")
                self.log_message(f"预约失败: {msg}")
                return False
        except Exception as e:
            messagebox.showerror("错误", f"预约失败: {str(e)}")
            self.log_message(f"预约失败: {str(e)}")
            return False
            
    # ===== 空间预约相关方法 =====
    # 仅修改空间预约相关方法，保留其他方法不变

    def get_space_premises_info(self):
        """获取空间预约的场馆信息"""
        try:
            self.client.set_booking_type('2')  # 设置为空间预约模式
            self.log_message("正在获取可预约空间场馆信息...")
            premises_list = self.client.get_premises()
            if premises_list:
                self.space_premises_combo['values'] = premises_list
                self.log_message(f"获取到 {len(premises_list)} 个场馆")
            else:
                self.log_message("获取场馆信息失败")
        except Exception as e:
            self.log_message(f"获取场馆信息失败: {str(e)}")

    def on_space_premises_select(self, event):
        """处理空间预约场馆选择事件"""
        try:
            selected = self.space_premises_combo.get()
            if not selected:
                return
                
            premises_id = selected.split(' - ')[0]
            self.log_message(f"选择场馆: {selected}")
            
            # 获取楼层信息
            storey_list = self.client.get_storeys(premises_id)
            self.space_storey_combo['values'] = storey_list
            self.space_storey_combo.set('')
            self.space_combo.set('')
            self.title_combo.set('')
            self.time_slot_combo.set('')
            
            self.log_message(f"获取到 {len(storey_list)} 个楼层")
        except Exception as e:
            self.log_message(f"选择场馆失败: {str(e)}")

    def on_space_storey_select(self, event):
        """处理空间预约楼层选择事件"""
        try:
            selected = self.space_storey_combo.get()
            if not selected:
                return
                
            storey_id = selected.split(' - ')[0]
            self.log_message(f"选择楼层: {selected}")
            
            # 获取区域信息
            area_list = self.client.get_areas(storey_id)
            self.space_combo['values'] = area_list
            self.space_combo.set('')
            self.title_combo.set('')
            self.time_slot_combo.set('')
            
            self.log_message(f"获取到 {len(area_list)} 个区域")
        except Exception as e:
            self.log_message(f"选择楼层失败: {str(e)}")

    def on_space_select(self, event):
        """处理空间预约区域选择事件"""
        try:
            selected = self.space_combo.get()
            if not selected:
                return
                
            premise_id = self.space_premises_combo.get().split(' - ')[0]
            room_id = selected.split(' - ')[0]
            self.log_message(f"选择空间: {selected}")
            
            # 清空树形视图
            self.clear_tree()
            
            # 获取空间详情
            space_detail = self.client.get_space_detail(room_id)

            # 直接设置为单人研习（5SCXX空间只有这个选项）
            self.title_combo['values'] = ['单人研习']
            self.title_combo.set('单人研习')  # 默认选中

            if space_detail:
                self.log_message(f"获取空间详情成功: {selected}")

                # 获取日期
                time_slots = self.client.get_space_time_slots(premise_id, room_id)
                if time_slots:
                    # 显示时间段选项
                    self.time_slot_combo['values'] = [slot['display'] for slot in time_slots]
                    self.log_message(f"获取到 {len(time_slots)} 个可用时间段")

                    # 检查是否真的可预约（不是已满状态）
                    available_slots = [slot for slot in time_slots if '已满' not in slot['display']]
                    status_text = '可预约' if available_slots else '已满'
                    self.seats_tree.insert('', 'end', values=(room_id, selected.split(' - ')[1], status_text))
                else:
                    self.time_slot_combo['values'] = []
                    self.log_message("该空间今日暂无可预约时间段")
                    self.seats_tree.insert('', 'end', values=(room_id, selected.split(' - ')[1], '无可用时段'))
            else:
                self.log_message("获取空间详情失败，使用默认设置")
                # 即使获取详情失败，也尝试获取时间段
                time_slots = self.client.get_space_time_slots(premise_id, room_id)
                if time_slots:
                    self.time_slot_combo['values'] = [slot['display'] for slot in time_slots]
                    available_slots = [slot for slot in time_slots if '已满' not in slot['display']]
                    status_text = '可预约' if available_slots else '已满'
                    self.seats_tree.insert('', 'end', values=(room_id, selected.split(' - ')[1], status_text))
                else:
                    self.time_slot_combo['values'] = []
                    self.seats_tree.insert('', 'end', values=(room_id, selected.split(' - ')[1], '无可用时段'))
        except Exception as e:
            self.log_message(f"选择空间失败: {str(e)}")

    def confirm_space_booking(self):
        """确认空间预约"""
        try:
            if not self.space_combo.get():
                messagebox.showerror("错误", "请选择空间")
                return False
                
            if not self.title_combo.get():
                messagebox.showerror("错误", "请选择标题")
                return False
            
            if not self.time_slot_combo.get():
                messagebox.showerror("错误", "请选择日期")
                return False
            
            # 在空间预约中，空间ID就是区域ID
            premise_id = self.space_premises_combo.get().split(' - ')[0]
            room_id = self.space_combo.get().split(' - ')[0]
            date_slot = self.time_slot_combo.get().split('(')[0]
            
            # 直接使用单人研习的标题ID（固定值）
            title_id = '1'  # 单人研习的ID通常是1
            title = '单人研习'

            # 查找对应的时间段ID
            date = None
            date_slots = self.client.get_space_time_slots(premise_id, room_id)
            for slot in date_slots:
                if slot['date'] == date_slot:
                    date = slot['date']
                    start_time = slot['start_time']
                    end_time = slot['end_time']
                    break
                    
            if not date:
                messagebox.showerror("错误", "无法找到对应的时间段ID")
                return False
                
            self.log_message(f"正在预约空间: {self.space_combo.get()}, 日期: {date}")
            
            # 确认预约
            success, msg = self.client.confirm_space_booking(room_id, date, start_time, end_time, title, title_id)
            
            if success:
                messagebox.showinfo("成功", "空间预约成功！")
                self.log_message(f"预约成功: {self.space_combo.get()}, 日期: {date}")
                # 预约成功后刷新五层状态
                self.refresh_fifth_floor_status()
                return True
            else:
                messagebox.showerror("错误", f"预约失败: {msg}")
                self.log_message(f"预约失败: {msg}")
                return False
        except Exception as e:
            messagebox.showerror("错误", f"预约失败: {str(e)}")
            self.log_message(f"预约失败: {str(e)}")
            return False

    def background_timer(self):
        """后台定时器"""
        try:
            while self.timer_on:
                # 计算距离下一次预约时间的秒数
                now = datetime.now()
                delta = (self.timer_time - now).total_seconds() * 1000
                self.log_message(f"距离开始预约还有 {'{:.2f}'.format(delta/1000)} 秒")
                if delta <= 0:
                    # 时间到，开始预约
                    self.log_message('时间到，开始预约')
                    self.timer_switch_var.set(False)
                    self.timer_on = False
                    if self.booking_type == '1':
                        confirm = self.confirm_booking
                    else:
                        confirm = self.confirm_space_booking
                    attempts = 0
                    while attempts < 3 and not confirm():
                        attempts += 1
                        self.log_message(f"第 {attempts} / 3 次尝试预约失败")
                    return
                if delta <= 1000:
                    time.sleep(delta / 1000)
                else:
                    time.sleep(1)
        except Exception as e:
            self.log_message(f"后台定时器异常: {str(e)}")
            self.timer_switch_var.set(False)
            self.timer_on = False


    def get_timer(self):
        """获取预约倒计时"""
        if not self.timer_var.get():
            return None
        try: 
            times = self.timer_var.get().split(':')
            if len(times)!= 3:
                if len(times) == 2:
                    times.append('0')
                else:
                    return None
            times = [int(t) for t in times]
            if times[0] < 0 or times[1] < 0 or times[2] < 0:
                return None
            if times[0] > 23 or times[1] > 59 or times[2] > 59:
                return None
            # 获取当日定时预约时间
            today = datetime.date(datetime.now())
            time = dtime(hour=times[0], minute=times[1], second=times[2])
            today_time = datetime.combine(today, time)
            if today_time < datetime.now():
                return None
            return today_time
        except:
            return None

    def set_timer_switch(self, status):
        if status:
            if (not self.timer_on) and ((not self.timer_thread) or (not self.timer_thread.is_alive())):
                self.timer_on = True
                self.timer_time = self.get_timer()
                self.timer_thread = threading.Thread(target=self.background_timer)
                self.timer_thread.start()
                self.log_message(f'已开启定时预约：{self.timer_time}')
        else:
            self.timer_on = False
            self.log_message('已关闭定时预约')

    def switch_timer(self):
        """切换预约倒计时"""
        if self.timer_switch_var.get():
            if not self.get_timer():
                messagebox.showerror("错误", '请输入正确的定时时间格式（hh:mm:ss 或 hh:mm）\n定时时间不能小于当前时间')
                self.timer_switch_var.set(False)
                return
            if self.booking_type == '1':
                if not self.seat_var.get():
                    messagebox.showerror("错误", "请选择座位")
                    self.timer_switch_var.set(False)
                    return
            else:
                if not self.space_combo.get():
                    messagebox.showerror("错误", "请选择空间")
                    self.timer_switch_var.set(False)
                    return
                    
                if not self.title_combo.get():
                    messagebox.showerror("错误", "请选择标题")
                    self.timer_switch_var.set(False)
                    return
                
                if not self.time_slot_combo.get():
                    messagebox.showerror("错误", "请选择日期")
                    self.timer_switch_var.set(False)
                    return
            self.set_timer_switch(True)
        else:
            self.set_timer_switch(False)

    # ===== 5SCXX自动抢座功能 =====
    def switch_auto_grab(self):
        """切换5SCXX自动抢座功能"""
        if self.auto_grab_var.get():
            if not self.client.is_logged_in:
                messagebox.showerror("错误", "请先登录")
                self.auto_grab_var.set(False)
                return

            try:
                interval = int(self.grab_interval_var.get())
                if interval < 3:
                    messagebox.showerror("错误", "检查间隔不能小于3秒")
                    self.auto_grab_var.set(False)
                    return
            except ValueError:
                messagebox.showerror("错误", "请输入有效的检查间隔（秒）")
                self.auto_grab_var.set(False)
                return

            self.start_auto_grab()
        else:
            self.stop_auto_grab()

    def start_auto_grab(self):
        """启动自动抢座"""
        if not self.auto_grab_on and (not self.auto_grab_thread or not self.auto_grab_thread.is_alive()):
            self.auto_grab_on = True
            self.auto_grab_thread = threading.Thread(target=self.auto_grab_worker)
            self.auto_grab_thread.daemon = True
            self.auto_grab_thread.start()
            self.grab_status_var.set("运行中...")
            interval = self.grab_interval_var.get()
            self.log_message(f"[自动抢座] 🚀 已启动5SCXX自动抢座功能，检查间隔: {interval}秒")
            self.log_message("[自动抢座] 💡 提示: 详细运行状态将在此日志区域显示")

    def stop_auto_grab(self):
        """停止自动抢座"""
        self.auto_grab_on = False
        self.grab_status_var.set("已停止")
        self.log_message("[自动抢座] ⏹️ 已停止5SCXX自动抢座功能")

    def auto_grab_worker(self):
        """自动抢座工作线程"""
        import time

        check_count = 0
        while self.auto_grab_on:
            try:
                # 获取检查间隔
                interval = int(self.grab_interval_var.get())
                check_count += 1

                # 更新状态显示（简化版本，避免数字过大）
                if check_count <= 999:
                    self.grab_status_var.set(f"检查中({check_count})")
                else:
                    self.grab_status_var.set("检查中(999+)")

                # 详细信息输出到日志
                self.log_message(f"[自动抢座] 第{check_count}次检查5SCXX空间状态...")

                # 检查5SCXX空间状态
                available_rooms = self.check_5scxx_availability()

                if available_rooms:
                    self.grab_status_var.set("发现空闲!")
                    self.log_message(f"[自动抢座] 🎯 发现可用5SCXX空间: {len(available_rooms)}个")

                    # 更新空闲列表显示
                    self.update_available_5scxx_display(available_rooms)

                    # 尝试预约第一个可用空间
                    room = available_rooms[0]
                    self.grab_status_var.set("尝试预约...")
                    self.log_message(f"[自动抢座] 🚀 尝试预约: {room['name']}")
                    success = self.try_grab_5scxx_room(room)

                    if success:
                        self.grab_status_var.set("预约成功!")
                        self.log_message(f"[自动抢座] 🎉 成功抢到5SCXX空间: {room['name']}")
                        # 抢座成功后停止自动抢座
                        self.auto_grab_var.set(False)
                        self.auto_grab_on = False
                        break
                    else:
                        self.grab_status_var.set("预约失败")
                        self.log_message(f"[自动抢座] ❌ 预约失败: {room['name']}")
                else:
                    # 简化状态显示
                    if check_count <= 999:
                        self.grab_status_var.set(f"无空闲({check_count})")
                    else:
                        self.grab_status_var.set("无空闲(999+)")

                    # 每10次检查输出一次日志，避免日志过多
                    if check_count % 10 == 0:
                        self.log_message(f"[自动抢座] 已检查{check_count}次，暂无空闲5SCXX空间")

                # 等待指定间隔
                for i in range(interval):
                    if not self.auto_grab_on:
                        break
                    time.sleep(1)

            except Exception as e:
                self.grab_status_var.set("检查异常")
                self.log_message(f"[自动抢座] ⚠️ 异常: {str(e)}")
                time.sleep(30)  # 出错时等待30秒再重试

        # 线程结束时更新状态
        if not self.auto_grab_on:
            self.grab_status_var.set("未启动")
            self.log_message("[自动抢座] 已停止")

    def check_5scxx_availability(self):
        """检查5SCXX空间可用性"""
        try:
            # 获取五层空间状态
            rooms = self.client.get_fifth_floor_rooms()

            # 筛选出可用的5SCXX空间
            available_rooms = []
            for room in rooms:
                if '5SC' in room['name'] and room['status']:  # status为True表示空闲
                    available_rooms.append(room)

            return available_rooms
        except Exception as e:
            self.log_message(f"检查5SCXX可用性失败: {str(e)}")
            return []

    def try_grab_5scxx_room(self, room):
        """尝试抢占指定的5SCXX空间"""
        try:
            # 设置为空间预约模式
            self.client.set_booking_type('2')

            # 获取空间详情和时间段
            space_detail = self.client.get_space_detail(room['id'])
            time_slots = self.client.get_space_time_slots("1", room['id'])  # 主图五层

            if not time_slots:
                return False

            # 选择第一个可用时间段
            available_slot = None
            for slot in time_slots:
                if '已满' not in slot['display']:
                    available_slot = slot
                    break

            if not available_slot:
                return False

            # 尝试预约
            success, msg = self.client.confirm_space_booking(
                room['id'],
                available_slot['date'],
                available_slot['start_time'],
                available_slot['end_time'],
                '单人研习',
                '1'  # 单人研习的ID
            )

            if success:
                # 刷新五层状态
                self.refresh_fifth_floor_status()

            return success

        except Exception as e:
            self.log_message(f"抢座异常: {str(e)}")
            return False

    # ===== 空闲5SCXX列表管理 =====
    def refresh_available_5scxx(self):
        """刷新空闲5SCXX列表"""
        try:
            self.log_message("正在刷新空闲5SCXX列表...")
            available_rooms = self.check_5scxx_availability()
            self.update_available_5scxx_display(available_rooms)
            self.log_message(f"刷新完成，发现 {len(available_rooms)} 个空闲5SCXX空间")
        except Exception as e:
            self.log_message(f"刷新空闲5SCXX列表失败: {str(e)}")

    def update_available_5scxx_display(self, available_rooms):
        """更新空闲5SCXX显示"""
        try:
            # 清空列表
            self.available_5scxx_listbox.delete(0, tk.END)

            # 添加空闲空间
            for room in available_rooms:
                display_text = f"{room['name']} ({room['date']}) - 空闲"
                self.available_5scxx_listbox.insert(tk.END, display_text)

            # 如果有空闲空间，选中第一个
            if available_rooms:
                self.available_5scxx_listbox.selection_set(0)

        except Exception as e:
            self.log_message(f"更新空闲5SCXX显示失败: {str(e)}")

    def quick_grab_selected(self):
        """快速预约选中的5SCXX空间"""
        try:
            selection = self.available_5scxx_listbox.curselection()
            if not selection:
                messagebox.showwarning("提示", "请先选择要预约的5SCXX空间")
                return

            # 获取选中的空间信息
            selected_text = self.available_5scxx_listbox.get(selection[0])
            room_name = selected_text.split(' (')[0]

            # 从可用空间列表中找到对应的空间
            available_rooms = self.check_5scxx_availability()
            selected_room = None
            for room in available_rooms:
                if room['name'] == room_name:
                    selected_room = room
                    break

            if not selected_room:
                messagebox.showerror("错误", "选中的空间已不可用，请刷新列表")
                return

            # 确认预约
            result = messagebox.askyesno("确认预约", f"确定要预约 {room_name} 吗？")
            if result:
                self.log_message(f"正在预约选中的5SCXX空间: {room_name}")
                success = self.try_grab_5scxx_room(selected_room)

                if success:
                    messagebox.showinfo("成功", f"成功预约 {room_name}！")
                    self.log_message(f"🎉 快速预约成功: {room_name}")
                    # 刷新列表
                    self.refresh_available_5scxx()
                    self.refresh_fifth_floor_status()
                else:
                    messagebox.showerror("失败", f"预约 {room_name} 失败")
                    self.log_message(f"快速预约失败: {room_name}")

        except Exception as e:
            messagebox.showerror("错误", f"快速预约失败: {str(e)}")
            self.log_message(f"快速预约异常: {str(e)}")

