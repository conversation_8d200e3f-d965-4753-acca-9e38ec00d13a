{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [{"startedDateTime": "2025-04-16T11:41:42.303Z", "id": "page_2", "title": "https://booking.lib.zju.edu.cn/h5/index.html", "pageTimings": {"onContentLoad": 262.71499996073544, "onLoad": 266.0269999760203}}], "entries": [{"_connectionId": "919", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "subscribe$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 16026}, {"functionName": "p", "scriptId": "87", "url": "https://booking.lib.zju.edu.cn/h5/assets/Appointment.1740556229652.js", "lineNumber": 0, "columnNumber": 742}, {"functionName": "", "scriptId": "87", "url": "https://booking.lib.zju.edu.cn/h5/assets/Appointment.1740556229652.js", "lineNumber": 0, "columnNumber": 610}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/index/subscribe", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "753"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2918, "bodySize": 753, "postData": {"mimeType": "application/json", "text": "{\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=84"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 1897, "mimeType": "application/json", "compression": 1212, "text": "{\"code\":1,\"msg\":\"操作成功\",\"data\":[{\"id\":\"43028\",\"status\":\"3\",\"beginDate\":\"2025-04-16\",\"endDate\":\"2025-04-16\",\"beginTime\":\"2025-04-16 18:45:00\",\"endTime\":\"2025-04-16 22:30:00\",\"signIn\":\"1\",\"signintime\":\"2025-04-16 19:20:15.123\",\"signOut\":null,\"area_id\":\"138\",\"space\":\"138\",\"space_id\":\"138\",\"no\":\"138\",\"enname\":\"\",\"spaceCategory\":\"2\",\"type\":2,\"parentId\":\"57\",\"nameMerge\":\"主馆-五层-5SC27(W)\",\"areaName\":\"主馆-五层-5SC27(W)\",\"ennameMerge\":\"-5 Floor-\",\"isSingle\":\"1\",\"ROW_NUMBER\":\"1\",\"SYS_WEB_MYYUYUE_SIGNIN\":\"0\",\"SYS_WEB_MYYUYUE_LEAVE\":\"1\",\"SYS_WEB_MYYUYUE_RIGHTBACK\":\"1\",\"earlierPeriodsSignIn\":-1,\"statusname\":\"使用中\",\"showTime\":\"2025-04-16 18:45:00 至 22:30:00\",\"earlierPeriods\":\"0\",\"lastSigninTime\":0,\"oksign\":0,\"flag_in\":0,\"flag_out\":1,\"flag_leave\":0,\"cancel\":0,\"use_time\":22,\"web_plane\":\"\\/home\\/<USER>\\/first\\/area\\/57\\/floor.jpg\",\"image_url\":\"https:\\/\\/booking.lib.zju.edu.cn\\/home\\/<USER>\\/first\\/area\\/57\\/floor.jpg\",\"only_cancel\":0},{\"id\":\"42927\",\"status\":\"2\",\"beginDate\":\"2025-04-17\",\"endDate\":\"2025-04-17\",\"beginTime\":\"2025-04-17 08:30:00\",\"endTime\":\"2025-04-17 22:30:00\",\"signIn\":null,\"signintime\":\"2025-04-16 19:41:41\",\"signOut\":null,\"area_id\":\"147\",\"space\":\"147\",\"space_id\":\"147\",\"no\":\"147\",\"enname\":\"\",\"spaceCategory\":\"2\",\"type\":2,\"parentId\":\"57\",\"nameMerge\":\"主馆-五层-5SC05(E)\",\"areaName\":\"主馆-五层-5SC05(E)\",\"ennameMerge\":\"-5 Floor-\",\"isSingle\":\"1\",\"ROW_NUMBER\":\"2\",\"SYS_WEB_MYYUYUE_SIGNIN\":\"0\",\"SYS_WEB_MYYUYUE_LEAVE\":\"1\",\"SYS_WEB_MYYUYUE_RIGHTBACK\":\"1\",\"earlierPeriodsSignIn\":-1,\"statusname\":\"预约成功\",\"showTime\":\"2025-04-17 08:30:00 至 22:30:00\",\"earlierPeriods\":\"0\",\"lastSigninTime\":\"2025-04-17 09:30:00\",\"oksign\":1,\"flag_in\":1,\"flag_out\":0,\"flag_leave\":0,\"cancel\":1,\"use_time\":0,\"web_plane\":\"\\/home\\/<USER>\\/first\\/area\\/57\\/floor.jpg\",\"image_url\":\"https:\\/\\/booking.lib.zju.edu.cn\\/home\\/<USER>\\/first\\/area\\/57\\/floor.jpg\",\"only_cancel\":0}]}"}, "redirectURL": "", "headersSize": 823, "bodySize": 685, "_transferSize": 1508, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.661Z", "time": 282.34500001417473, "timings": {"blocked": 1.8780000457689165, "dns": -1, "ssl": -1, "connect": -1, "send": 0.19100000000000006, "wait": 279.7230000067502, "receive": 0.552999961655587, "_blocked_queueing": 0.6940000457689166, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "954", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "notice", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 16128}, {"functionName": "Q", "scriptId": "83", "url": "https://booking.lib.zju.edu.cn/h5/assets/Notice.1740556229652.js", "lineNumber": 0, "columnNumber": 719}, {"functionName": "", "scriptId": "83", "url": "https://booking.lib.zju.edu.cn/h5/assets/Notice.1740556229652.js", "lineNumber": 0, "columnNumber": 424}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/index/notice", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "772"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2915, "bodySize": 772, "postData": {"mimeType": "application/json", "text": "{\"limit\":3,\"page\":1,\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=84"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 460, "mimeType": "application/json", "compression": 139, "text": "{\"code\":1,\"data\":{\"total\":8,\"per_page\":3,\"current_page\":1,\"last_page\":3,\"data\":[{\"id\":\"43\",\"title\":\"浙江大学图书馆座位预约系统升级通知\",\"displayNo\":\"0\",\"create_time\":\"2025-01-09\",\"ROW_NUMBER\":\"1\"},{\"id\":\"42\",\"title\":\"图书馆关于网络维护的通知\",\"displayNo\":\"0\",\"create_time\":\"2025-01-06\",\"ROW_NUMBER\":\"2\"},{\"id\":\"41\",\"title\":\"关于单人多日研习间调整的通知\",\"displayNo\":\"0\",\"create_time\":\"2024-12-11\",\"ROW_NUMBER\":\"3\"}]}}"}, "redirectURL": "", "headersSize": 823, "bodySize": 321, "_transferSize": 1144, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.661Z", "time": 63.245999976061285, "timings": {"blocked": 1.8669999692700803, "dns": -1, "ssl": -1, "connect": -1, "send": 0.09000000000000008, "wait": 60.61000001703203, "receive": 0.678999989759177, "_blocked_queueing": 0.5539999692700803, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1074", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "activity", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 16323}, {"functionName": "W", "scriptId": "83", "url": "https://booking.lib.zju.edu.cn/h5/assets/Notice.1740556229652.js", "lineNumber": 0, "columnNumber": 821}, {"functionName": "", "scriptId": "83", "url": "https://booking.lib.zju.edu.cn/h5/assets/Notice.1740556229652.js", "lineNumber": 0, "columnNumber": 428}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/activity/list", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "786"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2916, "bodySize": 786, "postData": {"mimeType": "application/json", "text": "{\"limit\":3,\"page\":1,\"available\":1,\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Length", "value": "40"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=93"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 20, "mimeType": "application/json", "compression": -20, "text": "{\"code\":1,\"data\":[]}"}, "redirectURL": "", "headersSize": 815, "bodySize": 40, "_transferSize": 855, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.661Z", "time": 61.446000006981194, "timings": {"blocked": 1.7810000173524023, "dns": -1, "ssl": -1, "connect": -1, "send": 0.08199999999999996, "wait": 59.18700000108778, "receive": 0.39599998854100704, "_blocked_queueing": 0.7820000173524022, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1034", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "getBanner", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 15816}, {"functionName": "re", "scriptId": "84", "url": "https://booking.lib.zju.edu.cn/h5/assets/index_m.1740556229652.js", "lineNumber": 0, "columnNumber": 4400}, {"functionName": "", "scriptId": "84", "url": "https://booking.lib.zju.edu.cn/h5/assets/index_m.1740556229652.js", "lineNumber": 0, "columnNumber": 3011}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/index/banner", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "753"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2915, "bodySize": 753, "postData": {"mimeType": "application/json", "text": "{\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=90"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 168, "mimeType": "application/json", "compression": -19, "text": "{\"code\":1,\"data\":[{\"id\":\"33\",\"content\":\"https:\\/\\/booking.lib.zju.edu.cn\\/upload\\/default\\/20231222\\/1703229826640936.jpg\",\"link\":\"\",\"displayNo\":\"0\",\"ROW_NUMBER\":\"1\"}]}"}, "redirectURL": "", "headersSize": 823, "bodySize": 187, "_transferSize": 1010, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.661Z", "time": 1061.2819999805652, "timings": {"blocked": 1.7289999841861428, "dns": -1, "ssl": -1, "connect": -1, "send": 0.07500000000000007, "wait": 1058.856999982599, "receive": 0.6210000137798488, "_blocked_queueing": 0.7699999841861427, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1080", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "booking_rules", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 16430}, {"functionName": "ce", "scriptId": "84", "url": "https://booking.lib.zju.edu.cn/h5/assets/index_m.1740556229652.js", "lineNumber": 0, "columnNumber": 4478}, {"functionName": "", "scriptId": "84", "url": "https://booking.lib.zju.edu.cn/h5/assets/index_m.1740556229652.js", "lineNumber": 0, "columnNumber": 3016}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/index/booking_rules", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "753"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2922, "bodySize": 753, "postData": {"mimeType": "application/json", "text": "{\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=91"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 53036, "mimeType": "application/json", "compression": 47380, "text": "{\"code\":1,\"msg\":\"操作成功\",\"data\":{\"seat\":\"<p>（1）.本预约座位系统面向浙江大学在校师生用户使用。实行预约入座制的阅览室座位，使用者需对号入座，未预约者不得使用座位。<\\/p><p style=\\\"line-height: 1;\\\">（2）.可预约时间：当日及次日8:30-22:30；每天早上7:00，系统开放次日座位预约（<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>主馆二楼全天学习空间仅提供当日预约，24小时开放；次日零点后，如继续使用座位，需再次扫码预约。<\\/strong><\\/span>）。<\\/p><p style=\\\"line-height: 1;\\\">（3）.预约生效及失效：当日预约即时生效，当天有效。预约次日则次日8:30生效，全日段有效。每人每天仅可取消一次已生效未签到的预约；预约生效后，需在30分钟内刷卡入馆。30分钟内未刷卡入馆，预约自动失效，预约人记违约一次。馆内当日预约，实时生效，无须刷卡。<\\/p><p style=\\\"line-height: 1;\\\">（4）座位保留：<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>中途刷卡出馆，座位自动保留20分钟；午餐时段10:30-13:00及晚餐时段16:30-19:00出馆，座位自动保留1小时。过时自动释放座位，如需使用请重新预约。<\\/strong><\\/span><\\/p><p style=\\\"line-height: 1;\\\">（5）.违约处罚：违约累计3次，座位预约资格冻结一周。以上违约处罚将于每个自然年首日自动清零。<\\/p><p style=\\\"line-height: 1;\\\">（6）.为了更好地利用座位资源，如需离馆不再使用座位者，建议主动操作“提前离座”或“完全离开” ，图书馆将采取积极开放的机制鼓励文明离馆，方便他人。<\\/p>\",\"discussion\":\"<p><strong>主馆多人研讨间使用须知<\\/strong><\\/p><p>1.读者可预约当日或次日的多人研讨间，研讨间每次预约最短 1 小时，最长 4 小时；<\\/p><p>2.研讨间开放时间为每日 8:30--22:30；<\\/p><p>3.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p>4. 8人研讨间除预约申请人外，还需要至少添加 3 位参与人员学工号。 14 人研讨间除预约申请人外，还需要至少添加 6 位参与人员学工号，14 人间需要通过人工审核后方可使用；<\\/p><p>5.每人每日可使用 (含预约与参与) 研讨间 2 次；<\\/p><p>6.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p>7.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约开始 30 分钟后，若成员签到人数未达到研讨间最少使用人数，记预约人违约一次，但保留本次预约。签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p>研讨间使用完毕后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，擦拭干净白板，关好门窗，关闭电源及房门，否则记预约人违约一次。<\\/p><p>使用研讨间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p><br><\\/p><p><strong>主馆单人间一日使用须知<\\/strong><\\/p><p>单人研习间仅供一人研究学习使用。预约成功后，读者不得预约同一时段的座位与其它研讨间。<\\/p><p>1.每日 7:00 系统开放预约，读者可预约当日或次日的单人研习间，预约成功后可使用 1 天；<\\/p><p>2.单人研习间每日开放时间 8:30-22:30；<\\/p><p>3.单人研习间使用过程中，中途刷卡出馆，研习间当日内始终保留；如因个人原因需要提前结束使用，请点击 “结束使用”；<\\/p><p>4.预约生效及失效：预约当日，从预约时间开始生效，全日段有效；预约次日，则次日 8:30 生效，全日段有效。预约次日，读者可在次日 7:30 分前取消预约，每人每天仅可取消一次；<\\/p><p>5.预约生效后，需在 60 分钟内签到进入单人研习间，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功：<\\/p><p>6.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约生效后，60 分钟内未签到，预约自动失效，该房间将自动释放，并记预约人违约一次。<\\/p><p>单人研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，否则记预约人违约一次。<\\/p><p>单人研习间仅供预约人本人研究学习使用，如有违反，记预约人违约一次。<\\/p><p>使用研习间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p><br><\\/p><p><strong>主馆单人间多日使用须知<\\/strong><\\/p><p>单人间一次可预约多天的区域为：5SC07 (E) 至 5SC08 (E)，共 2 间。<\\/p><p>1.每周一 7:00 系统开放预约单人研习间，每次可预约本自然周内 1 至 7 天的连续多日使用权限；<\\/p><p>2.单人多日研习间每日开放时间 8:30-22:30；<\\/p><p>3.单人多日研习间使用过程中，中途刷卡出馆，房间在当日内始终保留；<\\/p><p>4.预约生效及失效：预约后，从预约选择的开始日期的开始时间生效。如因个人原因需要提前结束使用，请点击 “取消”，取消后未开始的日期也将同时释放；<\\/p><p>5.预约生效后，如当日从 8：30 前预约的，需在 9：30 之前签到；当日从 8：30 之后预约的，需在预约后 60 分钟内签到；预约非当日的需在生效日 9：30 之前签到，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p>6.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约生效后，在预约的有效期内，每日 9:30 之前均需完成一次签到，否则记预约人违约一次；<\\/p><p>单人多日研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，每周日 22:30 清场，若未带走个人物品，记预约人违约一次。<\\/p><p>单人研习间仅限本人使用，不得转赠其他人使用，否则记预约人违约一次；<\\/p><p><br><\\/p><p style=\\\"text-align: start;\\\"><strong>主馆媒体工坊（试开放期间）预约使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学在馆校师生<\\/p><p style=\\\"text-align: start;\\\">二、预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">读者可预约每周五的媒体工坊，包括演播室、录音室和多功能摄影室，寒暑假、节假日除外<\\/p><p style=\\\"text-align: start;\\\">预约时间：提前7天开放预约（即前一周的周五 7：00）<\\/p><p style=\\\"text-align: start;\\\">预约时长：每人次最少预约1小时，最多预约2小时<\\/p><p style=\\\"text-align: start;\\\">预约人数：每次系统预约限制1人，支持同行者共同使用。同行人数最多5人，如超过5人，请预约成功后与老师单独联系。联系电话：0571-88206071<\\/p><p style=\\\"text-align: start;\\\">三、媒体工坊介绍<\\/p><p style=\\\"text-align: start;\\\">演播室：支持录制课程、录制演讲<\\/p><p style=\\\"text-align: start;\\\">录音室：支持录制音乐、录制访谈<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：支持拍摄人像或静物<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>紫金港基础馆三楼IC空间提供蓝绿红三个研讨空间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、 面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学图书馆座位预约系统面向浙江大学在校师生。<\\/p><p style=\\\"text-align: start;\\\">二、 预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">预约时间：研究空间提前5天开放预约。<\\/p><p style=\\\"text-align: start;\\\">签到时间：需在空间申请有效开始30分钟内完成4位组员人脸或刷卡签到，无临时离开和签离。<\\/p><p style=\\\"text-align: start;\\\">签到方式：在签到时间内通过空间门禁。<\\/p><p style=\\\"text-align: start;\\\">二、使用规则<\\/p><p style=\\\"text-align: start;\\\">1、读者预约5日内的研讨室。<\\/p><p style=\\\"text-align: start;\\\">2、预约时须填写必要的预约申请，多人间研讨室需要提交其他参与人员学号。<\\/p><p style=\\\"text-align: start;\\\">3、研讨室开放时间为每日8：00--22：00，研讨室每次预约最短1小时，最长4小时。<\\/p><p style=\\\"text-align: start;\\\">4、每人每日可预约使用研讨室2次，每人每日可2次<\\/p><p style=\\\"text-align: start;\\\">5、预约成功后,开始时间前3分钟内可刷卡签到。距离预约开始前60分钟，可取消预约。取消具体操作：点击‘我的--研讨室预约’取消预约。<\\/p><p style=\\\"text-align: start;\\\">6、预约成功后,全部成员须按时刷卡签到；研讨室至少需要4人刷卡签到；预约开始30分钟后，若成员刷卡签到人数未达到研讨室最少使用人数4人，系统记录申请人违约一次。违约三次后将被暂停使用图书馆的座位与空间的预约权限7天。<\\/p><p style=\\\"text-align: start;\\\">7、读者进入研讨室后须首先查看设备是否完好，有问题请及时联系工作人员（联系方式在预约网页“房间信息”或门口刷卡机上查看），否则，本次设备故障由预约人负责。<\\/p><p style=\\\"text-align: start;\\\">8、研究空间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关好门窗，关闭电源及房门。<\\/p>\",\"entrance\":\"<p>入馆预约规则中文<\\/p>\",\"bigdiscussion\":\"<p>大研讨室预约规则中文<\\/p>\",\"isShowSeatText\":\"1\",\"isShowRoomText\":\"1\",\"isShowActivityText\":\"1\",\"isShowLockerText\":\"1\",\"locker_rule\":\"书柜的中文弹窗使用须知\",\"locker_rule_en\":\"书柜的英文弹窗使用须知\",\"locker_declare\":\"书柜的中文使用规则说明\",\"locker_declare_en\":\"书柜的英文使用规则说明\",\"activity_alert_notice\":null,\"activity_alert_notice_en\":\"英文活动弹窗须知\",\"activity_rule\":\"<P>1.活动申请受理时间为8:30-11:30，14:00-17:00。<P>\\n<P>2.场地可提供多媒体设备投影、音响、话筒等设备，如发现设备异常请及时与场地管理员联系。<P>\\n<P>3.申请及审批单位意见请按顺序签章。<P>\\n<p>4.场地活动预约请在使用日期前7天内提交。<\\/p>\\n<P>5. 请不要携带食物进入场地内食用。<P>\\n<P>6.请自觉爱护公共财物和场地内的设备，发现设备异常请及时与工作人员联系，损坏公物及设施设备者须照价赔偿。<P>\\n<P>7.使用场地请遵守安全规范，注意个人安全，不擅自使用场地内多媒体设备。如因未遵守本规定，在使用场地过程中出现任何意外状况，由场地使用人承担相应责任。<P>\\n<P>8.活动结束后，请自觉及时清理场地垃圾，并将桌椅复原，保证室内干净整齐。<P>\",\"activity_rule_en\":\"英文活动申请预约规则\",\"seat_rule\":\"<p>（1）.本预约座位系统面向浙江大学在校师生用户使用。实行预约入座制的阅览室座位，使用者需对号入座，未预约者不得使用座位。<\\/p><p style=\\\"line-height: 1;\\\">（2）.可预约时间：当日及次日8:30-22:30；每天早上7:00，系统开放次日座位预约（<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>主馆二楼全天学习空间仅提供当日预约，24小时开放；次日零点后，如继续使用座位，需再次扫码预约。<\\/strong><\\/span>）。<\\/p><p style=\\\"line-height: 1;\\\">（3）.预约生效及失效：当日预约即时生效，当天有效。预约次日则次日8:30生效，全日段有效。每人每天仅可取消一次已生效未签到的预约；预约生效后，需在30分钟内刷卡入馆。30分钟内未刷卡入馆，预约自动失效，预约人记违约一次。馆内当日预约，实时生效，无须刷卡。<\\/p><p style=\\\"line-height: 1;\\\">（4）座位保留：<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>中途刷卡出馆，座位自动保留20分钟；午餐时段10:30-13:00及晚餐时段16:30-19:00出馆，座位自动保留1小时。过时自动释放座位，如需使用请重新预约。<\\/strong><\\/span><\\/p><p style=\\\"line-height: 1;\\\">（5）.违约处罚：违约累计3次，座位预约资格冻结一周。以上违约处罚将于每个自然年首日自动清零。<\\/p><p style=\\\"line-height: 1;\\\">（6）.为了更好地利用座位资源，如需离馆不再使用座位者，建议主动操作“提前离座”或“释放座位” ，图书馆将采取积极开放的机制鼓励文明离馆，方便他人。<\\/p>\",\"seat_rule_en\":\"<p>（1）.本预约座位系统面向浙江大学在校师生用户使用。实行预约入座制的阅览室座位，使用者需对号入座，未预约者不得使用座位。<\\/p><p style=\\\"line-height: 1;\\\">（2）.可预约时间：当日及次日8:30-22:30；每天早上7:00，系统开放次日座位预约（<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>主馆二楼全天学习空间仅提供当日预约，24小时开放；次日零点后，如继续使用座位，需再次扫码预约。<\\/strong><\\/span>）。<\\/p><p style=\\\"line-height: 1;\\\">（3）.预约生效及失效：当日预约即时生效，当天有效。预约次日则次日8:30生效，全日段有效。每人每天仅可取消一次已生效未签到的预约；预约生效后，需在30分钟内刷卡入馆。30分钟内未刷卡入馆，预约自动失效，预约人记违约一次。馆内当日预约，实时生效，无须刷卡。<\\/p><p style=\\\"line-height: 1;\\\">（4）座位保留：<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>中途刷卡出馆，座位自动保留20分钟；午餐时段10:30-13:00及晚餐时段16:30-19:00出馆，座位自动保留1小时。过时自动释放座位，如需使用请重新预约。<\\/strong><\\/span><\\/p><p style=\\\"line-height: 1;\\\">（5）.违约处罚：违约累计3次，座位预约资格冻结一周。以上违约处罚将于每个自然年首日自动清零。<\\/p><p style=\\\"line-height: 1;\\\">（6）.为了更好地利用座位资源，如需离馆不再使用座位者，建议主动操作“提前离座”或“释放座位” ，图书馆将采取积极开放的机制鼓励文明离馆，方便他人。<\\/p>\",\"seat_declare\":\"<p>（1）.本预约座位系统面向浙江大学在校师生用户使用。实行预约入座制的阅览室座位，使用者需对号入座，未预约者不得使用座位。<\\/p><p style=\\\"line-height: 1;\\\">（2）.可预约时间：当日及次日8:30-22:30；每天早上7:00，系统开放次日座位预约（<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>主馆二楼全天学习空间仅提供当日预约，24小时开放；次日零点后，如继续使用座位，需再次扫码预约。<\\/strong><\\/span>）。<\\/p><p style=\\\"line-height: 1;\\\">（3）.预约生效及失效：当日预约即时生效，当天有效。预约次日则次日8:30生效，全日段有效。每人每天仅可取消一次已生效未签到的预约；预约生效后，需在30分钟内刷卡入馆。30分钟内未刷卡入馆，预约自动失效，预约人记违约一次。馆内当日预约，实时生效，无须刷卡。<\\/p><p style=\\\"line-height: 1;\\\">（4）座位保留：<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>中途刷卡出馆，座位自动保留20分钟；午餐时段10:30-13:00及晚餐时段16:30-19:00出馆，座位自动保留1小时。过时自动释放座位，如需使用请重新预约。<\\/strong><\\/span><\\/p><p style=\\\"line-height: 1;\\\">（5）.违约处罚：违约累计3次，座位预约资格冻结一周。以上违约处罚将于每个自然年首日自动清零。<\\/p><p style=\\\"line-height: 1;\\\">（6）.为了更好地利用座位资源，如需离馆不再使用座位者，建议主动操作“提前离座”或“完全离开” ，图书馆将采取积极开放的机制鼓励文明离馆，方便他人。<\\/p>\",\"seat_declare_en\":\"<p>（1）.本预约座位系统面向浙江大学在校师生用户使用。实行预约入座制的阅览室座位，使用者需对号入座，未预约者不得使用座位。<\\/p><p style=\\\"line-height: 1;\\\">（2）.可预约时间：当日及次日8:30-22:30；每天早上7:00，系统开放次日座位预约（<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>主馆二楼全天学习空间仅提供当日预约，24小时开放；次日零点后，如继续使用座位，需再次扫码预约。<\\/strong><\\/span>）。<\\/p><p style=\\\"line-height: 1;\\\">（3）.预约生效及失效：当日预约即时生效，当天有效。预约次日则次日8:30生效，全日段有效。每人每天仅可取消一次已生效未签到的预约；预约生效后，需在30分钟内刷卡入馆。30分钟内未刷卡入馆，预约自动失效，预约人记违约一次。馆内当日预约，实时生效，无须刷卡。<\\/p><p style=\\\"line-height: 1;\\\">（4）座位保留：<span style=\\\"color: rgb(207, 19, 34);\\\"><strong>中途刷卡出馆，座位自动保留20分钟；午餐时段10:30-13:00及晚餐时段16:30-19:00出馆，座位自动保留1小时。过时自动释放座位，如需使用请重新预约。<\\/strong><\\/span><\\/p><p style=\\\"line-height: 1;\\\">（5）.违约处罚：违约累计3次，座位预约资格冻结一周。以上违约处罚将于每个自然年首日自动清零。<\\/p><p style=\\\"line-height: 1;\\\">（6）.为了更好地利用座位资源，如需离馆不再使用座位者，建议主动操作“提前离座”或“完全离开” ，图书馆将采取积极开放的机制鼓励文明离馆，方便他人。<\\/p>\",\"seminar_rule\":\"<p style=\\\"text-align: left;\\\"><strong>主馆多人研讨间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">1.读者可预约当日或次日的研讨间，研讨间每次预约最短1小时，最长4小时；<\\/p><p style=\\\"text-align: start;\\\">2.研讨间开放时间为每日8:30--22:30；<\\/p><p style=\\\"text-align: start;\\\">3.预约成功后，读者可在预约开始前30分钟取消预约；<\\/p><p style=\\\"text-align: start;\\\">4.8人研讨间除预约申请人外，还需要至少添加3位参与人员学工号。 14人研讨间除预约申请人外，还需要至少添加6位参与人员学工号，14人间需要通过人工审核后方可使用；<\\/p><p style=\\\"text-align: start;\\\">5.每人每日可使用(含预约与参与)研讨间2次；<\\/p><p style=\\\"text-align: start;\\\">6.预约成功后，读者可在预约开始前30分钟取消预约；<\\/p><p style=\\\"text-align: start;\\\">7.违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约3次，将暂停使用所有空间预约权限7天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约开始30分钟后，若成员刷卡签到人数未达到研讨间最少使用人数，记预约人违约一次，但保留本次预约。<\\/p><p style=\\\"text-align: start;\\\">研讨间使用完毕后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，擦拭干净白板，关好门窗，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研讨间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: left;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>主馆单人间一日使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供一人研究学习使用。预约成功后，读者不得预约同一时段的座位与其它<span style=\\\"color: red;\\\">研讨间。<\\/span><\\/p><p style=\\\"text-align: start;\\\">1、每日7:00系统开放预约，读者可预约当日或次日的单人研习间，预约成功后可使用1天；<\\/p><p style=\\\"text-align: start;\\\">2、单人研习间每日开放时间8:30-22:30；<\\/p><p style=\\\"text-align: start;\\\">3、单人研习间使用过程中，中途刷卡出馆，研习间当日内始终保留；<span style=\\\"color: red;\\\">如因个人原因需要提前结束使用，请点击“结束使用”<\\/span><\\/p><p style=\\\"text-align: start;\\\">4、预约生效及失效：预约当日，从预约时间开始生效，全日段有效；预约次日，则次日8:30生效，全日段有效。预约次日，读者可在次日7:30分前取消预约，每人每天仅可取消一次；<\\/p><p style=\\\"text-align: start;\\\">5，预约生效后，需在60分钟内签到进入单人研习间。<\\/p><p style=\\\"text-align: start;\\\">6，违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约3次，将暂停使用所有空间预约权限7天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约生效后，60分钟<span style=\\\"color: black;\\\">内未签到，<\\/span><span style=\\\"color: red;\\\">预约自动失效，<\\/span>预约人记违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供预约人本人研究学习使用，如有违反，记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研习间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p><strong>主馆单人间多日使用须知<\\/strong><\\/p><p><span style=\\\"color: rgb(0, 0, 0);\\\">单人间一次可预约多天的区域为：<\\/span><span style=\\\"color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); font-size: 13px;\\\">5SC07(E)至5SC08(E)，共2间。<\\/span><\\/p><p>1. &nbsp; &nbsp;每周一7:00系统开放预约单人研习间，每次可预约本自然周内1至7天的连续多日使用权限；<\\/p><p>2. &nbsp; &nbsp;单人多日研习间每日开放时间8:30-22:30；<\\/p><p>3. &nbsp; &nbsp;单人多日研习间使用过程中，中途刷卡出馆，房间在当日内始终保留；<\\/p><p>4. &nbsp; &nbsp;预约生效及失效：预约后，从预约选择的开始日期的开始时间生效。如因个人原因需要提前结束使用，请点击“取消”，取消后未开始的日期也将同时释放；<\\/p><p>5. &nbsp; &nbsp;预约生效后，如当日从8：30前预约的，需在9：30之前签到；当日从8：30之后预约的，需在预约后60分钟内签到；预约非当日的需在生效日9：30之前签到；<\\/p><p>6. &nbsp; &nbsp;违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约3次，将暂停使用所有空间预约权限7天。以下情况视为违约：<\\/p><p>预约生效后，在预约的有效期内，每日9:30之前均需完成一次签到，否则记预约人违约一次；<\\/p><p>单人多日研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，每周日22:30清场，若未带走个人物品，记预约人违约一次。<\\/p><p>单人研习间仅限本人使用，不得转赠其他人使用，否则记预约人违约一次；<\\/p><p><br><\\/p><p style=\\\"text-align: start;\\\"><strong>主馆媒体工坊（试开放期间）预约使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学在馆校师生<\\/p><p style=\\\"text-align: start;\\\">二、预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">读者可预约每周五的媒体工坊，包括演播室、录音室和多功能摄影室，寒暑假、节假日除外<\\/p><p style=\\\"text-align: start;\\\">预约时间：提前7天开放预约（即前一周的周五 7：00）<\\/p><p style=\\\"text-align: start;\\\">预约时长：每人次最少预约1小时，最多预约2小时<\\/p><p style=\\\"text-align: start;\\\">预约人数：每次系统预约限制1人，支持同行者共同使用。同行人数最多5人，如超过5人，请预约成功后与老师单独联系。联系电话：0571-88206071<\\/p><p style=\\\"text-align: start;\\\">三、媒体工坊介绍<\\/p><p style=\\\"text-align: start;\\\">演播室：支持录制课程、录制演讲<\\/p><p style=\\\"text-align: start;\\\">录音室：支持录制音乐、录制访谈<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：支持拍摄人像或静物<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>紫金港基础馆三楼IC空间提供蓝绿红三个研讨空间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、 面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学图书馆座位预约系统面向浙江大学在校师生。<\\/p><p style=\\\"text-align: start;\\\">二、 预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">预约时间：研究空间提前5天开放预约。<\\/p><p style=\\\"text-align: start;\\\">签到时间：需在空间申请有效开始30分钟内完成4位组员人脸或刷卡签到，无临时离开和签离。<\\/p><p style=\\\"text-align: start;\\\">签到方式：在签到时间内通过空间门禁。<\\/p><p style=\\\"text-align: start;\\\">二、使用规则<\\/p><p style=\\\"text-align: start;\\\">1、读者预约5日内的研讨室。<\\/p><p style=\\\"text-align: start;\\\">2、预约时须填写必要的预约申请，多人间研讨室需要提交其他参与人员学号。<\\/p><p style=\\\"text-align: start;\\\">3、研讨室开放时间为每日8：00--22：00，研讨室每次预约最短1小时，最长4小时。<\\/p><p style=\\\"text-align: start;\\\">4、每人每日可预约使用研讨室2次，每人每日可2次<\\/p><p style=\\\"text-align: start;\\\">5、预约成功后,开始时间前3分钟内可刷卡签到。距离预约开始前60分钟，可取消预约。取消具体操作：点击‘我的--研讨室预约’取消预约。<\\/p><p style=\\\"text-align: start;\\\">6、预约成功后,全部成员须按时刷卡签到；研讨室至少需要4人刷卡签到；预约开始30分钟后，若成员刷卡签到人数未达到研讨室最少使用人数4人，系统记录申请人违约一次。违约三次后将被暂停使用图书馆的座位与空间的预约权限7天。<\\/p><p style=\\\"text-align: start;\\\">7、读者进入研讨室后须首先查看设备是否完好，有问题请及时联系工作人员（联系方式在预约网页“房间信息”或门口刷卡机上查看），否则，本次设备故障由预约人负责。<\\/p><p style=\\\"text-align: start;\\\">8、研究空间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关好门窗，关闭电源及房门。<\\/p>\",\"seminar_rule_en\":\"<p style=\\\"text-align: left;\\\"><strong>主馆多人研讨间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">1.读者可预约当日或次日的研讨间，研讨间每次预约最短1小时，最长4小时；<\\/p><p style=\\\"text-align: start;\\\">2.研讨间开放时间为每日08：30--22：30；<\\/p><p style=\\\"text-align: start;\\\">3.8人研讨间除预约申请人外，还需要至少添加3位参与人员学工号。 14人研讨间除预约申请人外，还需要至少添加6位参与人员学工号，14人间需要通过人工审核后方可使用。<\\/p><p style=\\\"text-align: start;\\\">4.每人每日可使用（含预约与参与）研讨间2次；<\\/p><p style=\\\"text-align: start;\\\">5.预约成功后，读者可在预约开始前30分钟取消预约；<\\/p><p style=\\\"text-align: start;\\\">6.预约成功后,预约成员须按时刷卡签到；8人研讨间至少需要4人刷卡签到，14人研讨间至少需要7人刷卡签到；<\\/p><p style=\\\"text-align: start;\\\">7.违约规则：<\\/p><p style=\\\"text-align: start;\\\">研讨间违约三次后将被暂停使用图书馆研讨空间的预约权限7天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约开始30分钟后，若成员刷卡签到人数未达到研讨间最少使用人数，记预约人违约一次，但保留本次预约。<\\/p><p style=\\\"text-align: start;\\\">研讨间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，擦拭干净白板，关好门窗，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研讨间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: left;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>主馆单人间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供一人研究学习使用。预约成功后，读者不得预约同一时段的座位与其它<span style=\\\"color: red;\\\">研讨间。<\\/span><\\/p><p style=\\\"text-align: start;\\\">1、每日7:00系统开放预约，读者可预约当日或次日的单人研习间，预约成功后可使用1天；<\\/p><p style=\\\"text-align: start;\\\">2、单人研习间每日开放时间8:30-22:30；<\\/p><p style=\\\"text-align: start;\\\">3、单人研习间使用过程中，中途刷卡出馆，研习间当日内始终保留；<span style=\\\"color: red;\\\">如因个人原因需要提前结束使用，请点击“结束使用”<\\/span><\\/p><p style=\\\"text-align: start;\\\">4、预约生效及失效：预约当日，从预约时间开始生效，全日段有效；预约次日，则次日8:30生效，全日段有效。预约次日，读者可在次日7:30分前取消预约，每人每天仅可取消一次；<\\/p><p style=\\\"text-align: start;\\\">5，预约生效后，需在60分钟内签到进入单人研习间。<\\/p><p style=\\\"text-align: start;\\\">6，违约规则：<\\/p><p style=\\\"text-align: start;\\\">单人研习间累计违约3次，将暂停使用空间预约权限7天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约生效后，60分钟<span style=\\\"color: black;\\\">内未签到，<\\/span><span style=\\\"color: red;\\\">预约自动失效，<\\/span>预约人记违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供预约人本人研究学习使用，如有违反，记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研习间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\"><strong>主馆媒体工坊（试开放期间）预约使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学在校师生<\\/p><p style=\\\"text-align: start;\\\">二、预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">读者可预约每周四的媒体工坊，<span style=\\\"color: rgb(225, 60, 57);\\\">(如需导入伴奏或带走录制的作品，请自行携带U盘)。<\\/span><\\/p><p style=\\\"text-align: start;\\\">预约时间：提前7天开放预约（即前一周的周四 7：00）<\\/p><p style=\\\"text-align: start;\\\">预约时长：每人次最少预约1小时，最多预约2小时<\\/p><p style=\\\"text-align: start;\\\">预约人数：每次系统预约限制1人，支持同行者共同使用。同行人数最多5人，如超过5人，请预约成功后与老师单独联系。联系电话：0571-88206071<\\/p><p style=\\\"text-align: start;\\\">三、开放规则<\\/p><p style=\\\"text-align: start;\\\">媒体工坊每周轮流开放，包括演播室、录音室和多功能摄影室，具体见下<\\/p><p style=\\\"text-align: start;\\\">演播室：3.14、4.11、5.9、5.30、6.20<\\/p><p style=\\\"text-align: start;\\\">录音室：3.7、3.28、4.25、5.23、6.13<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：3.21、4.18、5.16、6.6<\\/p><p style=\\\"text-align: start;\\\">四、媒体工坊介绍<\\/p><p style=\\\"text-align: start;\\\">演播室：支持录制课程、录制演讲<\\/p><p style=\\\"text-align: start;\\\">录音室：支持录制音乐、录制访谈<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：支持拍摄人像或静物<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>紫金港基础馆三楼IC空间提供蓝绿红三个研讨空间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、 面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学图书馆座位预约系统面向浙江大学在校师生。<\\/p><p style=\\\"text-align: start;\\\">二、 预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">预约时间：研究空间提前5天开放预约。<\\/p><p style=\\\"text-align: start;\\\">签到时间：需在空间申请有效开始30分钟内完成4位组员人脸或刷卡签到，无临时离开和签离。<\\/p><p style=\\\"text-align: start;\\\">签到方式：在签到时间内通过空间门禁。<\\/p><p style=\\\"text-align: start;\\\">二、使用规则<\\/p><p style=\\\"text-align: start;\\\">1、读者预约5日内的研讨室。<\\/p><p style=\\\"text-align: start;\\\">2、预约时须填写必要的预约申请，多人间研讨室需要提交其他参与人员学号。<\\/p><p style=\\\"text-align: start;\\\">3、研讨室开放时间为每日8：00--22：00，研讨室每次预约最短1小时，最长4小时。<\\/p><p style=\\\"text-align: start;\\\">4、每人每日可预约使用研讨室2次，每人每日可2次<\\/p><p style=\\\"text-align: start;\\\">5、预约成功后,开始时间前3分钟内可刷卡签到。距离预约开始前60分钟，可取消预约。取消具体操作：点击‘我的--研讨室预约’取消预约。<\\/p><p style=\\\"text-align: start;\\\">6、预约成功后,全部成员须按时刷卡签到；研讨室至少需要4人刷卡签到；预约开始30分钟后，若成员刷卡签到人数未达到研讨室最少使用人数4人，系统记录申请人违约一次。违约三次后将被暂停使用图书馆的座位与空间的预约权限7天。<\\/p><p style=\\\"text-align: start;\\\">7、读者进入研讨室后须首先查看设备是否完好，有问题请及时联系工作人员（联系方式在预约网页“房间信息”或门口刷卡机上查看），否则，本次设备故障由预约人负责。<\\/p><p style=\\\"text-align: start;\\\">8、研究空间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关好门窗，关闭电源及房门。<\\/p>\",\"seminar_declare\":\"<p><strong>主馆多人研讨间使用须知<\\/strong><\\/p><p>1.读者可预约当日或次日的多人研讨间，研讨间每次预约最短 1 小时，最长 4 小时；<\\/p><p>2.研讨间开放时间为每日 8:30--22:30；<\\/p><p>3.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p>4. 8人研讨间除预约申请人外，还需要至少添加 3 位参与人员学工号。 14 人研讨间除预约申请人外，还需要至少添加 6 位参与人员学工号，14 人间需要通过人工审核后方可使用；<\\/p><p>5.每人每日可使用 (含预约与参与) 研讨间 2 次；<\\/p><p>6.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p>7.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约开始 30 分钟后，若成员签到人数未达到研讨间最少使用人数，记预约人违约一次，但保留本次预约。签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p>研讨间使用完毕后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，擦拭干净白板，关好门窗，关闭电源及房门，否则记预约人违约一次。<\\/p><p>使用研讨间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p><br><\\/p><p><strong>主馆单人间一日使用须知<\\/strong><\\/p><p>单人研习间仅供一人研究学习使用。预约成功后，读者不得预约同一时段的座位与其它研讨间。<\\/p><p>1.每日 7:00 系统开放预约，读者可预约当日或次日的单人研习间，预约成功后可使用 1 天；<\\/p><p>2.单人研习间每日开放时间 8:30-22:30；<\\/p><p>3.单人研习间使用过程中，中途刷卡出馆，研习间当日内始终保留；如因个人原因需要提前结束使用，请点击 “结束使用”；<\\/p><p>4.预约生效及失效：预约当日，从预约时间开始生效，全日段有效；预约次日，则次日 8:30 生效，全日段有效。预约次日，读者可在次日 7:30 分前取消预约，每人每天仅可取消一次；<\\/p><p>5.预约生效后，需在 60 分钟内签到进入单人研习间，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功：<\\/p><p>6.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约生效后，60 分钟内未签到，预约自动失效，该房间将自动释放，并记预约人违约一次。<\\/p><p>单人研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，否则记预约人违约一次。<\\/p><p>单人研习间仅供预约人本人研究学习使用，如有违反，记预约人违约一次。<\\/p><p>使用研习间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p><br><\\/p><p><strong>主馆单人间多日使用须知<\\/strong><\\/p><p>单人间一次可预约多天的区域为：5SC07 (E) 至 5SC08 (E)，共 2 间。<\\/p><p>1.每周一 7:00 系统开放预约单人研习间，每次可预约本自然周内 1 至 7 天的连续多日使用权限；<\\/p><p>2.单人多日研习间每日开放时间 8:30-22:30；<\\/p><p>3.单人多日研习间使用过程中，中途刷卡出馆，房间在当日内始终保留；<\\/p><p>4.预约生效及失效：预约后，从预约选择的开始日期的开始时间生效。如因个人原因需要提前结束使用，请点击 “取消”，取消后未开始的日期也将同时释放；<\\/p><p>5.预约生效后，如当日从 8：30 前预约的，需在 9：30 之前签到；当日从 8：30 之后预约的，需在预约后 60 分钟内签到；预约非当日的需在生效日 9：30 之前签到，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p>6.违约规则：<\\/p><p>累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p>预约生效后，在预约的有效期内，每日 9:30 之前均需完成一次签到，否则记预约人违约一次；<\\/p><p>单人多日研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，每周日 22:30 清场，若未带走个人物品，记预约人违约一次。<\\/p><p>单人研习间仅限本人使用，不得转赠其他人使用，否则记预约人违约一次；<\\/p><p><br><\\/p><p style=\\\"text-align: start;\\\"><strong>主馆媒体工坊（试开放期间）预约使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学在馆校师生<\\/p><p style=\\\"text-align: start;\\\">二、预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">读者可预约每周五的媒体工坊，包括演播室、录音室和多功能摄影室，寒暑假、节假日除外<\\/p><p style=\\\"text-align: start;\\\">预约时间：提前7天开放预约（即前一周的周五 7：00）<\\/p><p style=\\\"text-align: start;\\\">预约时长：每人次最少预约1小时，最多预约2小时<\\/p><p style=\\\"text-align: start;\\\">预约人数：每次系统预约限制1人，支持同行者共同使用。同行人数最多5人，如超过5人，请预约成功后与老师单独联系。联系电话：0571-88206071<\\/p><p style=\\\"text-align: start;\\\">三、媒体工坊介绍<\\/p><p style=\\\"text-align: start;\\\">演播室：支持录制课程、录制演讲<\\/p><p style=\\\"text-align: start;\\\">录音室：支持录制音乐、录制访谈<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：支持拍摄人像或静物<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: left;\\\"><strong>紫金港基础馆三楼IC空间提供蓝绿红三个研讨空间使用须知<\\/strong><\\/p><p style=\\\"text-align: start;\\\">一、 面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学图书馆座位预约系统面向浙江大学在校师生。<\\/p><p style=\\\"text-align: start;\\\">二、 预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">预约时间：研究空间提前5天开放预约。<\\/p><p style=\\\"text-align: start;\\\">签到时间：需在空间申请有效开始30分钟内完成4位组员人脸或刷卡签到，无临时离开和签离。<\\/p><p style=\\\"text-align: start;\\\">签到方式：在签到时间内通过空间门禁。<\\/p><p style=\\\"text-align: start;\\\">二、使用规则<\\/p><p style=\\\"text-align: start;\\\">1、读者预约5日内的研讨室。<\\/p><p style=\\\"text-align: start;\\\">2、预约时须填写必要的预约申请，多人间研讨室需要提交其他参与人员学号。<\\/p><p style=\\\"text-align: start;\\\">3、研讨室开放时间为每日8：00--22：00，研讨室每次预约最短1小时，最长4小时。<\\/p><p style=\\\"text-align: start;\\\">4、每人每日可预约使用研讨室2次，每人每日可2次<\\/p><p style=\\\"text-align: start;\\\">5、预约成功后,开始时间前3分钟内可刷卡签到。距离预约开始前60分钟，可取消预约。取消具体操作：点击‘我的--研讨室预约’取消预约。<\\/p><p style=\\\"text-align: start;\\\">6、预约成功后,全部成员须按时刷卡签到；研讨室至少需要4人刷卡签到；预约开始30分钟后，若成员刷卡签到人数未达到研讨室最少使用人数4人，系统记录申请人违约一次。违约三次后将被暂停使用图书馆的座位与空间的预约权限7天。<\\/p><p style=\\\"text-align: start;\\\">7、读者进入研讨室后须首先查看设备是否完好，有问题请及时联系工作人员（联系方式在预约网页“房间信息”或门口刷卡机上查看），否则，本次设备故障由预约人负责。<\\/p><p style=\\\"text-align: start;\\\">8、研究空间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关好门窗，关闭电源及房门。<\\/p>\",\"seminar_declare_en\":\"<p style=\\\"text-align: start;\\\">主馆多人研讨间使用须知<\\/p><p style=\\\"text-align: start;\\\">1.读者可预约当日或次日的多人研讨间，研讨间每次预约最短 1 小时，最长 4 小时；<\\/p><p style=\\\"text-align: start;\\\">2.研讨间开放时间为每日 8:30--22:30；<\\/p><p style=\\\"text-align: start;\\\">3.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p style=\\\"text-align: start;\\\">4. 8人研讨间除预约申请人外，还需要至少添加 3 位参与人员学工号。 14 人研讨间除预约申请人外，还需要至少添加 6 位参与人员学工号，14 人间需要通过人工审核后方可使用；<\\/p><p style=\\\"text-align: start;\\\">5.每人每日可使用 (含预约与参与) 研讨间 2 次；<\\/p><p style=\\\"text-align: start;\\\">6.预约成功后，读者可在预约开始前 30 分钟取消预约；<\\/p><p style=\\\"text-align: start;\\\">7.违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约开始 30 分钟后，若成员签到人数未达到研讨间最少使用人数，记预约人违约一次，但保留本次预约。签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p style=\\\"text-align: start;\\\">研讨间使用完毕后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，擦拭干净白板，关好门窗，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研讨间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\">主馆单人间一日使用须知<\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供一人研究学习使用。预约成功后，读者不得预约同一时段的座位与其它研讨间。<\\/p><p style=\\\"text-align: start;\\\">1.每日 7:00 系统开放预约，读者可预约当日或次日的单人研习间，预约成功后可使用 1 天；<\\/p><p style=\\\"text-align: start;\\\">2.单人研习间每日开放时间 8:30-22:30；<\\/p><p style=\\\"text-align: start;\\\">3.单人研习间使用过程中，中途刷卡出馆，研习间当日内始终保留；如因个人原因需要提前结束使用，请点击 “结束使用”；<\\/p><p style=\\\"text-align: start;\\\">4.预约生效及失效：预约当日，从预约时间开始生效，全日段有效；预约次日，则次日 8:30 生效，全日段有效。预约次日，读者可在次日 7:30 分前取消预约，每人每天仅可取消一次；<\\/p><p style=\\\"text-align: start;\\\">5.预约生效后，需在 60 分钟内签到进入单人研习间，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功：<\\/p><p style=\\\"text-align: start;\\\">6.违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约生效后，60 分钟内未签到，预约自动失效，该房间将自动释放，并记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，否则记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间仅供预约人本人研究学习使用，如有违反，记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">使用研习间，须遵守国家法律法规及学校、图书馆相关规章制度。<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\">主馆单人间多日使用须知<\\/p><p style=\\\"text-align: start;\\\">单人间一次可预约多天的区域为：5SC07 (E) 至 5SC08 (E)，共 2 间。<\\/p><p style=\\\"text-align: start;\\\">1.每周一 7:00 系统开放预约单人研习间，每次可预约本自然周内 1 至 7 天的连续多日使用权限；<\\/p><p style=\\\"text-align: start;\\\">2.单人多日研习间每日开放时间 8:30-22:30；<\\/p><p style=\\\"text-align: start;\\\">3.单人多日研习间使用过程中，中途刷卡出馆，房间在当日内始终保留；<\\/p><p style=\\\"text-align: start;\\\">4.预约生效及失效：预约后，从预约选择的开始日期的开始时间生效。如因个人原因需要提前结束使用，请点击 “取消”，取消后未开始的日期也将同时释放；<\\/p><p style=\\\"text-align: start;\\\">5.预约生效后，如当日从 8：30 前预约的，需在 9：30 之前签到；当日从 8：30 之后预约的，需在预约后 60 分钟内签到；预约非当日的需在生效日 9：30 之前签到，签到时需通过以下三种方式之一（钉钉扫码 \\/ 人脸识别 \\/ 刷校园卡）在预约房间门口的屏幕签到，方为签到成功。<\\/p><p style=\\\"text-align: start;\\\">6.违约规则：<\\/p><p style=\\\"text-align: start;\\\">累计违约 3 次，将暂停使用所有空间预约权限 7 天。以下情况视为违约：<\\/p><p style=\\\"text-align: start;\\\">预约生效后，在预约的有效期内，每日 9:30 之前均需完成一次签到，否则记预约人违约一次；<\\/p><p style=\\\"text-align: start;\\\">单人多日研习间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关闭电源及房门，每周日 22:30 清场，若未带走个人物品，记预约人违约一次。<\\/p><p style=\\\"text-align: start;\\\">单人研习间仅限本人使用，不得转赠其他人使用，否则记预约人违约一次；<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\">主馆媒体工坊（试开放期间）预约使用须知<\\/p><p style=\\\"text-align: start;\\\">一、面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学在馆校师生<\\/p><p style=\\\"text-align: start;\\\">二、预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">读者可预约每周五的媒体工坊，包括演播室、录音室和多功能摄影室，寒暑假、节假日除外<\\/p><p style=\\\"text-align: start;\\\">预约时间：提前7天开放预约（即前一周的周五 7：00）<\\/p><p style=\\\"text-align: start;\\\">预约时长：每人次最少预约1小时，最多预约2小时<\\/p><p style=\\\"text-align: start;\\\">预约人数：每次系统预约限制1人，支持同行者共同使用。同行人数最多5人，如超过5人，请预约成功后与老师单独联系。联系电话：0571-88206071<\\/p><p style=\\\"text-align: start;\\\">三、媒体工坊介绍<\\/p><p style=\\\"text-align: start;\\\">演播室：支持录制课程、录制演讲<\\/p><p style=\\\"text-align: start;\\\">录音室：支持录制音乐、录制访谈<\\/p><p style=\\\"text-align: start;\\\">多功能摄影室：支持拍摄人像或静物<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\"><br><\\/p><p style=\\\"text-align: start;\\\">紫金港基础馆三楼IC空间提供蓝绿红三个研讨空间使用须知<\\/p><p style=\\\"text-align: start;\\\">一、 面向人群<\\/p><p style=\\\"text-align: start;\\\">浙江大学图书馆座位预约系统面向浙江大学在校师生。<\\/p><p style=\\\"text-align: start;\\\">二、 预约范围与预约时间<\\/p><p style=\\\"text-align: start;\\\">预约时间：研究空间提前5天开放预约。<\\/p><p style=\\\"text-align: start;\\\">签到时间：需在空间申请有效开始30分钟内完成4位组员人脸或刷卡签到，无临时离开和签离。<\\/p><p style=\\\"text-align: start;\\\">签到方式：在签到时间内通过空间门禁。<\\/p><p style=\\\"text-align: start;\\\">二、使用规则<\\/p><p style=\\\"text-align: start;\\\">1、读者预约5日内的研讨室。<\\/p><p style=\\\"text-align: start;\\\">2、预约时须填写必要的预约申请，多人间研讨室需要提交其他参与人员学号。<\\/p><p style=\\\"text-align: start;\\\">3、研讨室开放时间为每日8：00--22：00，研讨室每次预约最短1小时，最长4小时。<\\/p><p style=\\\"text-align: start;\\\">4、每人每日可预约使用研讨室2次，每人每日可2次<\\/p><p style=\\\"text-align: start;\\\">5、预约成功后,开始时间前3分钟内可刷卡签到。距离预约开始前60分钟，可取消预约。取消具体操作：点击‘我的--研讨室预约’取消预约。<\\/p><p style=\\\"text-align: start;\\\">6、预约成功后,全部成员须按时刷卡签到；研讨室至少需要4人刷卡签到；预约开始30分钟后，若成员刷卡签到人数未达到研讨室最少使用人数4人，系统记录申请人违约一次。违约三次后将被暂停使用图书馆的座位与空间的预约权限7天。<\\/p><p style=\\\"text-align: start;\\\">7、读者进入研讨室后须首先查看设备是否完好，有问题请及时联系工作人员（联系方式在预约网页“房间信息”或门口刷卡机上查看），否则，本次设备故障由预约人负责。<\\/p><p style=\\\"text-align: start;\\\">8、研究空间使用完成后，使用者负责将室内物品及设备恢复使用前的状态，带走个人物品及废弃物，关好门窗，关闭电源及房门。<\\/p><p style=\\\"text-align: start;\\\"><br><\\/p>\"}}"}, "redirectURL": "", "headersSize": 823, "bodySize": 5656, "_transferSize": 6479, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.662Z", "time": 1116.7469999636523, "timings": {"blocked": 1.9929999454468488, "dns": -1, "ssl": -1, "connect": -1, "send": 0.1010000000000002, "wait": 1114.1589999819362, "receive": 0.49400003626942635, "_blocked_queueing": 0.8489999454468489, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "1061", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 4536}, {"functionName": "xhr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 2050}, {"functionName": "dispatchRequest$1", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 7981}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "Axios$1.request", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 43, "columnNumber": 12130}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 42, "columnNumber": 53855}, {"functionName": "getInvitations", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 17752}, {"functionName": "getInvitation", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 19163}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 2995}, {"functionName": "Store$1.dispatch", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 11938}, {"functionName": "Store$1.dispatch", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 60, "columnNumber": 10717}, {"functionName": "", "scriptId": "82", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.174055622965210.js", "lineNumber": 0, "columnNumber": 1197}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 35126}, {"functionName": "callWithErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19714}, {"functionName": "callWithAsyncErrorHandling", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 19840}, {"functionName": "y.__weh.y.__weh", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 34898}, {"functionName": "flushPostFlushCbs", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 22013}, {"functionName": "tr", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 63537}, {"functionName": "mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 45940}, {"functionName": "y.mount", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 0, "columnNumber": 92914}, {"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378183}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "", "scriptId": "78", "url": "https://booking.lib.zju.edu.cn/h5/assets/index.1740556229652.js", "lineNumber": 1081, "columnNumber": 378155}]}}}}, "_priority": "High", "_resourceType": "xhr", "cache": {}, "connection": "443", "pageref": "page_2", "request": {"method": "POST", "url": "https://booking.lib.zju.edu.cn/api/member/invitations", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "zh-CN,zh;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "753"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Host", "value": "booking.lib.zju.edu.cn"}, {"name": "Origin", "value": "https://booking.lib.zju.edu.cn"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://booking.lib.zju.edu.cn/h5/index.html"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "X-Requested-With", "value": "XMLHttpRequest"}, {"name": "lang", "value": "zh"}, {"name": "sec-ch-ua", "value": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"Windows\""}], "queryString": [], "cookies": [], "headersSize": 2921, "bodySize": 753, "postData": {"mimeType": "application/json", "text": "{\"authorization\":\"bearereyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjMyNDAxMDE2NTkiLCJuYW1lIjoiXHU2YzZhXHU4ODYxIiwibW9iaWxlIjoiMTMzNTcwMDgwNDAiLCJlbWFpbCI6bnVsbCwic3RhdHVzIjoiMSIsImpvaW5UaW1lIjoiMjAyNC0wOC0xMSAxMzoxNjowNC42MDAiLCJyb2xlTmFtZSI6Ilx1NjcyY1x1NzlkMVx1NzUxZiIsImRlcHROYW1lIjoiXHU2NzJhXHU3N2U1XHU5MGU4XHU5NWU4IiwibGFuZ3VhZ2UiOiIxIiwiaXNRdWlja1NlbGVjdCI6IjEiLCJwb3BVcCI6eyJ1c2VyU2hvd1NlYXRUZXh0RmxhZyI6MCwidXNlclNob3dSb29tVGV4dEZsYWciOjAsInVzZXJTaG93QWN0aXZpdHlUZXh0RmxhZyI6MCwidXNlclNob3dMb2NrZXJUZXh0RmxhZyI6MH0sIlJPV19OVU1CRVIiOiIxIiwiYXVkIjoiIiwiZXhwIjoxNzQ0ODA5Njk2LCJpYXQiOjE3NDQ4MDM2OTYsImlzcyI6IiIsImp0aSI6IjlkNTQ0ODljYjNmMDYwOTU1ZWEyOTJlZjM3M2RhMGMxIiwibmJmIjoxNzQ0ODAzNjk2LCJzdWIiOiIifQ.xWN4_0YgGyH5gRk49zgMRwOotme5NSY-Ftb5BOApJRA\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Access-Control-Allow-Origin", "value": "booking.lib.zju.edu.cn"}, {"name": "Cache-Control", "value": "no-cache, must-revalidate"}, {"name": "Connection", "value": "Keep-Alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Length", "value": "58"}, {"name": "Content-Security-Policy", "value": "default-src 'self' localhost:443 'unsafe-inline' 'unsafe-eval' blob: data: ;"}, {"name": "Content-Type", "value": "application/json; charset=utf-8"}, {"name": "Date", "value": "Wed, 16 Apr 2025 11:41:41 GMT"}, {"name": "Keep-Alive", "value": "timeout=5, max=94"}, {"name": "Server", "value": "Apache/2.4.43"}, {"name": "Strict-Transport-Security", "value": "max-age=63072000; includeSubdomains; preload"}, {"name": "Vary", "value": "Authorization,Accept-Encoding,User-Agent"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-Download-Options", "value": "value"}, {"name": "X-Frame-Options", "value": "SAMEORIGIN"}, {"name": "X-Permitted-Cross-Domain-Policies", "value": "value"}, {"name": "X-Powered-By", "value": "PHP/7.4.6"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}], "cookies": [], "content": {"size": 41, "mimeType": "application/json", "compression": -17, "text": "{\"code\":1,\"data\":{\"seminar\":0,\"count\":0}}"}, "redirectURL": "", "headersSize": 815, "bodySize": 58, "_transferSize": 873, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "************", "startedDateTime": "2025-04-16T11:41:42.664Z", "time": 1096.4780000504106, "timings": {"blocked": 1.0810000054985285, "dns": -1, "ssl": -1, "connect": -1, "send": 0.36, "wait": 1094.706999976851, "receive": 0.33000006806105375, "_blocked_queueing": 0.7040000054985285, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}